<template>
    <component :is="layoutComponent" v-if="layoutComponent" />
    <div v-else>Loading layout...</div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import AppAdmin from './layouts/AppAdmin.vue';
import AppFront from './layouts/AppUser.vue';

defineOptions({
  name: 'App'
});

const route = useRoute();

const layoutComponent = computed(() =>
  route.path.startsWith('/admin') ? AppAdmin : AppFront
);

onMounted(() => {
  document.title = `${import.meta.env.VITE_APP_NAME} - User Portal`;
});
</script>
