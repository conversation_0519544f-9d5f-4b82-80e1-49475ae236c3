import { reactive } from 'vue';

export const usePricingFormState = () => {
  const pricing = reactive({
    package_name: '',
    package_level: null,
    package_price: null,
    currency: '',
    risk_evaluation: { status: false, sessions: null, includes_diagnosis: false },
    fair_decision_analysis: { status: false, sessions: null, includes_diagnosis: false },
    training: false,
    online_consultancy_hours: null,
    in_person_services: {
      status: false,
      consultancy: false,
      consultancy_hours: null,
      evaluation: false,
      risk_evaluation_sessions: null,
      fair_decision_sessions: null,
      training: false
    },
    stripe_product_id:null,
    stripe_price_id:null,
  });

  return { pricing };
};
