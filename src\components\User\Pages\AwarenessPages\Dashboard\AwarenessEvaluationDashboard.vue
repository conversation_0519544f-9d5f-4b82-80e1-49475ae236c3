<template>
    <div class="awareness-evaluation-dashboard">
        <UserPageHeader
            background-color="linear-gradient(135deg, #059669 0%, #047857 100%)"
            icon="fa fa-graduation-cap"
            title="Awareness Training"
            subtitle="Enhance your AI knowledge through comprehensive training courses and assessments"
            action-icon="fa fa-plus"
            action-text="Start Course"
            :action-route="{ name: 'CourseList' }"
        />

        <div class="dashboard-container">
            <UserLoadingState v-if="loading" color="#059669" />
            
            <EmptyState 
                v-else-if="!evaluations.length"
                title="No Courses Available"
                description="There are currently no awareness courses available. Please check back later or contact your administrator."
                icon="fa fa-graduation-cap"
                primary-color="#059669"
            />
            
            <div v-else class="dashboard-content">
                <FeatureStats
                    title="Your Learning Progress"
                    primary-color="#059669"
                    :stats="[
                        { value: evaluations.length, label: 'Started Courses' },
                        { value: completedCoursesCount, label: 'Completed' },
                        { value: totalCertificates, label: 'Certificates Available' }
                    ]"
                />

                <div class="courses-grid">
                    <NewFeatureCard
                        primary-color="#059669"
                        secondary-color="#047857"
                        icon="fa fa-plus"
                        title="Start New Course"
                        description="Explore available training courses and expand your AI knowledge"
                        action-text="Browse Courses"
                        action-icon="fa fa-arrow-right"
                        :route="{ name: 'CourseList' }"
                    />

                    <UserCourseCard
                        v-for="course in evaluations"
                        :key="course._id"
                        :course="course"
                        :get-status-icon="getStatusIcon"
                        :get-status-label="getStatusLabel"
                        :get-status-class="getStatusClass"
                        :get-progress-class="getProgressClass"
                        :format-date="formatDate"
                        :navigate-to-course="navigateToCourse"
                        :download-course-certificate="downloadCourseCertificate"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import UserPageHeader from '@/components/General/UserPageHeader.vue';
import UserLoadingState from '@/components/General/UserLoadingState.vue';
import EmptyState from '@/components/General/EmptyState.vue';
import FeatureStats from '@/components/General/FeatureStats.vue';
import NewFeatureCard from '@/components/General/NewFeatureCard.vue';
import UserCourseCard from './UserCourseCard.vue';

const {
    loading,
    courses,
    getAwarenessEvaluationsWithProgress,
    totalCertificates,
    completedCoursesCount,
    navigateToCourse,
    downloadCourseCertificate,
    getStatusIcon,
    getStatusLabel,
    getProgressClass,
    getStatusClass,
    formatDate
} = useAwarenessActions();

const evaluations = computed(() => courses.value);

onMounted(() => {
    getAwarenessEvaluationsWithProgress();
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/Dashboard/AwarenessEvaluationDashboard.scss';
</style>
