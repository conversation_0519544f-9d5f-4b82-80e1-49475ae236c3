<template>
    <LessonForm :is-edit-mode="true" :lesson-data="lesson" :course-id="courseId" @submit="handleUpdateLesson"/>
</template>

<script setup>
import { watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAwarenessManagementActions } from '@/composables/awareness/useAwarenessManagementActions';
import { useLessonRouteParams } from '@/composables/awareness/useLessonRouteParams';
import LessonForm from './LessonForm.vue';

const route = useRoute();
const router = useRouter();
const { currentLesson: lesson, getLesson, updateLesson } = useAwarenessManagementActions();
const { courseId, topicId, lessonId } = useLessonRouteParams();
const emit = defineEmits(['refresh']);

const handleUpdateLesson = async (lessonData) => {
    await updateLesson(courseId.value, topicId.value, lessonId.value, lessonData);
    emit('refresh');
    router.push({ name: 'AdminLessonPreview', params: { course_id: courseId.value, topic_id: topicId.value, lesson_id: lessonId.value}});
};

const loadLesson = async () => {
    await getLesson(courseId.value, topicId.value, lessonId.value);
};

onMounted(() => {
    loadLesson();
});

watch(() => route.params.lesson_id, (newId, oldId) => {
    if (newId !== oldId) {
        loadLesson();
    }
});
</script>