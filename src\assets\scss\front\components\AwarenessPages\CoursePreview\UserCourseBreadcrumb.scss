.course-header {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  padding: 2rem;
  border-radius: 24px;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
  }

  .header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 2rem;
    min-height: 60px;
  }

  .course-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    font-size: 1.5rem;

    .breadcrumb-link {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: color 0.3s ease;

      &:hover {
        color: white;
      }

      i {
        font-size: 0.9rem;
      }
    }

    .breadcrumb-separator {
      color: rgba(255, 255, 255, 0.6);
      margin: 0 0.25rem;
    }

    .breadcrumb-current {
      color: white;
      font-weight: 500;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

@media (max-width: 1024px) {
  .course-header {
    .header-content {
      flex-direction: column;
      gap: 1.5rem;
      text-align: center;
    }
  }
}

@media (max-width: 768px) {
  .course-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 0;

    .course-breadcrumb {
      .breadcrumb-current {
        max-width: 200px;
      }
    }
  }
}

@media (max-width: 480px) {
  .course-header {
    padding: 1rem;

    .course-breadcrumb {
      .breadcrumb-link {
        font-size: 0.875rem;
      }

      .breadcrumb-current {
        font-size: 0.875rem;
      }
    }
  }
}
