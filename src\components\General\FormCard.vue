<template>
  <div class="card question-holder">
    <div class="card-header bg-white py-3 d-flex justify-content-between sticky-top">
      <h4 class="m-0"> <strong class="text-dark">{{ title }}</strong> </h4>
      <div class="">
        <router-link class="btn btn-sm btn-secondary rounded-pill px-3 me-2" :to="cancelRoute">Cancel</router-link>
        <button  type="submit" class="btn btn-sm btn-primary rounded-pill px-3" :disabled="loading">{{ submitText }}</button>
      </div>
    </div>
    <div class="card-body p-2 p-lg-3">
      <div class="row">
        <div class="col-lg-12 mb-1">
          <div class="w-100 p-4">
            <div class="error-report-g"></div>
            <slot />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: { type: String, required: true },
  cancelRoute: { type: Object, required: true },
  submitText: { type: String, default: 'Save Changes' },
  loading: { type: Boolean, default: false }
});
</script>
