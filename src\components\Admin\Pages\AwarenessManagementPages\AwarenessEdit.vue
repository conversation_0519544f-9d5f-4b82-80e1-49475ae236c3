<template>
    <AwarenessForm :is-edit-mode="true" :course-data="course" @submit="handleUpdateCourse" />
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { usePricingActions } from '@/composables/pricing/usePricingActions';
import { useAwarenessManagementActions } from '@/composables/awareness/useAwarenessManagementActions';
import AwarenessForm from './AwarenessForm.vue';

const route = useRoute();
const { pricing_packages, getAllPricing } = usePricingActions();
const { getSingleAwareness, updateAwareness } = useAwarenessManagementActions();

const course_id = ref(null);
const course = ref(null);

const getCourse = async () => {
    try {
        const { data } = await getSingleAwareness(course_id.value);
        course.value = data;
    } catch (error) {
        console.error('Error fetching course:', error);
    }
};

const handleUpdateCourse = async (courseData) => {
    try {
        await updateAwareness(course_id.value, courseData);
    } catch (error) {
        console.error('Error updating course:', error);
    }
};

onMounted(async () => {
    course_id.value = route.params.course_id;
    await getCourse();
    await getAllPricing('admin');
    if (course.value) {
        course.value.selected_package = pricing_packages.value.find((pkg) => pkg._id === course.value?.package_id) || null;
    }
});
</script>