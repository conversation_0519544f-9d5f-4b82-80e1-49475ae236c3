import { createApp } from 'vue'
import App from './App.vue'
import router from '@/routers'
import jQuery from "jquery";
import bootstrap from 'bootstrap/dist/js/bootstrap.bundle.js'
import axios from "axios";
import VueSweetAlert from 'vue-sweetalert2'
import 'sweetalert2/dist/sweetalert2.min.css'
import { setupRefreshInterceptor } from '@/Helpers/TokenRefresher';

window.$ = jQuery;
window.bootstrap = bootstrap;
axios.defaults.withCredentials = true
axios.defaults.baseURL = import.meta.env.VITE_API_URL
setupRefreshInterceptor();

function loadReCaptcha() {
    const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
    return new Promise((resolve, reject) => {
        if (window.grecaptcha) return resolve();
        const script = document.createElement("script");
        script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
        script.async = true;
        script.onload = () => resolve();
        script.onerror = () => reject("Failed to load reCAPTCHA script");
        document.head.appendChild(script);
    });
}


loadReCaptcha().then(() => {
  axios.get('/sanctum/csrf-cookie')
  .then(() => {
    const app = createApp(App)
    app.use(router, VueSweetAlert)
    app.mount('#app')
  })
}).catch(console.error);
