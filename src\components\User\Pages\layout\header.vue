<!-- src/components/User/Pages/layout/header.vue -->
<template>
    <header class="modern-header">
        <div class="header-container">
            <!-- Logo Section -->
            <div class="header-brand">
                <a 
                    href="https://raidot.ai" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="brand-link"
                >
                    <span class="brand-text">
                        Rai<span class="brand-accent">DOT</span>
                    </span>
                </a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button 
                class="mobile-menu-toggle"
                @click="toggleMobileMenu"
                :class="{ 'active': showMobileMenu }"
            >
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>

            <!-- Navigation Menu -->
            <nav class="main-nav" :class="{ 'mobile-open': showMobileMenu }">
                <router-link 
                    v-for="item in navigationItems" 
                    :key="item.name"
                    :to="{ name: item.route }" 
                    class="nav-link"
                    :class="{ 'active': isActiveRoute(item.route) }"
                    @click="closeMobileMenu"
                >
                    <i :class="item.icon" class="nav-icon"></i>
                    <span class="nav-label">{{ item.name }}</span>
                    <div class="nav-indicator"></div>
                </router-link>
            </nav>

            <!-- User Section -->
            <div class="user-section" v-if="UserInfo">
                <!-- Subscription Badge -->
                <div class="subscription-status">
                    <span 
                        class="subscription-badge" 
                        :class="subscriptionBadgeClass"
                    >
                        <i class="fa fa-star" v-if="UserInfo.subscription_type === 'premium'"></i>
                        {{ UserInfo.subscription_type === 'premium' ? 'Premium' : 'Free' }}
                    </span>
                </div>

                <!-- Notifications (placeholder for future feature) -->
                <div class="notifications">
                    <button class="notification-btn">
                        <i class="fa fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>

                <!-- User Menu -->
                <div class="user-menu" ref="userMenu">
                    <button 
                        class="user-trigger"
                        @click="toggleUserMenu"
                        :class="{ 'active': showUserMenu }"
                    >
                        <div class="user-avatar">
                            <img 
                                :src="userAvatar" 
                                :alt="UserInfo.name"
                                class="avatar-image"
                                @error="handleAvatarError"
                            >
                            <div class="online-indicator"></div>
                        </div>
                        <div class="user-details">
                            <span class="user-name">{{ UserInfo.name }}</span>
                            <span class="user-email">{{ UserInfo.email }}</span>
                        </div>
                        <i class="fa fa-chevron-down dropdown-arrow"></i>
                    </button>

                    <!-- Dropdown Menu -->
                    <div class="user-dropdown" :class="{ 'show': showUserMenu }">
                        <div class="dropdown-header">
                            <div class="dropdown-avatar">
                                <img :src="userAvatar" :alt="UserInfo.name">
                            </div>
                            <div class="dropdown-user-info">
                                <div class="dropdown-name">{{ UserInfo.name }}</div>
                                <div class="dropdown-email">{{ UserInfo.email }}</div>
                            </div>
                        </div>

                        <div class="dropdown-divider"></div>

                        <div class="dropdown-menu-items">
                            <router-link 
                                :to="{ name: 'Profile' }" 
                                class="dropdown-item"
                                @click="closeUserMenu"
                            >
                                <i class="fa fa-user item-icon"></i>
                                <span>Profile Settings</span>
                            </router-link>

                            <router-link 
                                :to="{ name: 'Pricing' }" 
                                class="dropdown-item"
                                @click="closeUserMenu"
                            >
                                <i class="fa fa-star item-icon"></i>
                                <span>
                                    {{ UserInfo.subscription_type === 'premium' ? 'Manage Plan' : 'Upgrade to Premium' }}
                                </span>
                                <span 
                                    v-if="UserInfo.subscription_type !== 'premium'" 
                                    class="upgrade-badge"
                                >
                                    New
                                </span>
                            </router-link>

                            <a href="#" class="dropdown-item">
                                <i class="fa fa-cog item-icon"></i>
                                <span>Settings</span>
                            </a>

                            <a href="#" class="dropdown-item">
                                <i class="fa fa-question-circle item-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </div>

                        <div class="dropdown-divider"></div>

                        <button 
                            @click="logout" 
                            class="dropdown-item logout-item"
                        >
                            <i class="fa fa-sign-out item-icon"></i>
                            <span>Sign Out</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Overlay -->
        <div 
            class="mobile-overlay" 
            :class="{ 'show': showMobileMenu }"
            @click="closeMobileMenu"
        ></div>
    </header>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export default {
    name: 'ModernHeader',
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            showUserMenu: false,
            showMobileMenu: false,
            navigationItems: [
                { 
                    name: 'Dashboard', 
                    route: 'Dashboard', 
                    icon: 'fa fa-tachometer-alt' 
                },
                { 
                    name: 'Risk Evaluation', 
                    route: 'RiskEvaluation', 
                    icon: 'fa fa-shield-alt' 
                },
                { 
                    name: 'Fairness Analysis', 
                    route: 'FairDecisionAnalysis', 
                    icon: 'fa fa-balance-scale' 
                },
                { 
                    name: 'Awareness', 
                    route: 'AwarenessEvaluation', 
                    icon: 'fa fa-graduation-cap' 
                }
            ]
        }
    },
    computed: {
        userAvatar() {
            const baseUrl = import.meta.env.VITE_API_URL;
            return this.UserInfo?.avatar 
                ? `${baseUrl}/storage/media/image/${this.UserInfo.avatar}`
                : '/img/user.png';
        },
        subscriptionBadgeClass() {
            return this.UserInfo?.subscription_type === 'premium' 
                ? 'premium' 
                : 'free';
        }
    },
    methods: {
        isActiveRoute(routeName) {
            return this.$route.name === routeName;
        },
        
        toggleUserMenu() {
            this.showUserMenu = !this.showUserMenu;
            this.showMobileMenu = false;
        },
        
        closeUserMenu() {
            this.showUserMenu = false;
        },
        
        toggleMobileMenu() {
            this.showMobileMenu = !this.showMobileMenu;
            this.showUserMenu = false;
        },
        
        closeMobileMenu() {
            this.showMobileMenu = false;
        },
        
        handleAvatarError(event) {
            event.target.src = '/img/user.png';
        },
        
        logout() {
            ApiService.GET(ApiRoutes.UserLogout, (res) => {
                if (res.status === 200) {
                    localStorage.removeItem('JwtToken');
                    localStorage.removeItem('UserInfo');
                    this.$router.push({ name: 'Login' });
                } else {
                    console.error('Logout failed:', res);
                }
            });
        },
        
        handleClickOutside(event) {
            if (this.$refs.userMenu && !this.$refs.userMenu.contains(event.target)) {
                this.showUserMenu = false;
            }
        }
    },
    mounted() {
        document.addEventListener('click', this.handleClickOutside);
        window.scrollTo(0, 0);
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutside);
    }
}
</script>

<style scoped>
.modern-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    height: 80px;
    position: relative;
}

/* Brand */
.header-brand {
    flex-shrink: 0;
    z-index: 1001;
}

.brand-link {
    text-decoration: none;
    display: block;
    transition: transform 0.3s ease;
}

.brand-link:hover {
    transform: scale(1.05);
}

.brand-text {
    font-size: 2.25rem;
    font-weight: 800;
    color: white;
    letter-spacing: -1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.brand-accent {
    color: #ef4444;
    text-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Navigation */
.main-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    color: #cbd5e1;
    text-decoration: none;
    border-radius: 16px;
    font-weight: 600;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-link.active {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
    box-shadow: 0 4px 20px rgba(121, 37, 199, 0.4);
}

.nav-icon {
    font-size: 1.125rem;
}

.nav-indicator {
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: #ef4444;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover .nav-indicator,
.nav-link.active .nav-indicator {
    width: 80%;
}

/* User Section */
.user-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.subscription-status {
    display: flex;
    align-items: center;
}

.subscription-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.3s ease;
}

.subscription-badge.premium {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.subscription-badge.free {
    background: rgba(148, 163, 184, 0.2);
    color: #cbd5e1;
    border: 1px solid rgba(203, 213, 225, 0.3);
}

/* Notifications */
.notifications {
    position: relative;
}

.notification-btn {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.notification-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ef4444;
    color: white;
    font-size: 0.625rem;
    font-weight: 700;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* User Menu */
.user-menu {
    position: relative;
}

.user-trigger {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.5rem;
    padding-right: 1rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.user-trigger:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.user-avatar {
    position: relative;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
    70% { box-shadow: 0 0 0 8px rgba(16, 185, 129, 0); }
    100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

.user-details {
    display: flex;
    flex-direction: column;
    text-align: left;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-email {
    font-size: 0.75rem;
    color: #cbd5e1;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.dropdown-arrow {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.user-trigger.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    top: calc(100% + 0.75rem);
    right: 0;
    background: white;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    z-index: 1002;
    overflow: hidden;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dropdown-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dropdown-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-user-info {
    flex: 1;
    min-width: 0;
}

.dropdown-name {
    font-weight: 700;
    color: #1f2937;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-email {
    color: #6b7280;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0;
}

.dropdown-menu-items {
    padding: 0.5rem 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 0.875rem 1.5rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    position: relative;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    transition: width 0.3s ease;
}

.dropdown-item:hover {
    background: #f8fafc;
    color: #7925c7;
    transform: translateX(4px);
}

.dropdown-item:hover::before {
    width: 3px;
}

.item-icon {
    width: 18px;
    text-align: center;
    color: #6b7280;
    transition: color 0.2s ease;
}

.dropdown-item:hover .item-icon {
    color: #7925c7;
}

.upgrade-badge {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    font-size: 0.625rem;
    font-weight: 700;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: auto;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px rgba(239, 68, 68, 0.3); }
    to { box-shadow: 0 0 15px rgba(239, 68, 68, 0.6); }
}

.logout-item {
    margin-top: 0.5rem;
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
    color: #dc2626;
}

.logout-item:hover {
    background: #fef2f2;
    color: #b91c1c;
}

.logout-item:hover .item-icon {
    color: #b91c1c;
}

/* Mobile Styles */
@media (max-width: 1024px) {
    .main-nav {
        gap: 0.25rem;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .nav-label {
        display: none;
    }
    
    .nav-icon {
        font-size: 1.25rem;
    }
    
    .user-details {
        display: none;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
    }
    
    .brand-text {
        font-size: 1.75rem;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .main-nav {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: #1e293b;
        flex-direction: column;
        padding: 2rem 1rem;
        gap: 0.5rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 1000;
    }
    
    .main-nav.mobile-open {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-link {
        width: 100%;
        justify-content: flex-start;
        padding: 1rem 1.5rem;
        margin: 0;
        border-radius: 12px;
    }
    
    .nav-label {
        display: inline;
    }
    
    .user-section {
        gap: 0.5rem;
    }
    
    .subscription-status,
    .notifications {
        display: none;
    }
    
    .user-dropdown {
        min-width: 260px;
    }
}

.mobile-overlay {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.mobile-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Enhanced focus states for accessibility */
.nav-link:focus,
.user-trigger:focus,
.dropdown-item:focus,
.notification-btn:focus {
    outline: 2px solid #7925c7;
    outline-offset: 2px;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Loading states */
.nav-link.loading {
    pointer-events: none;
    opacity: 0.6;
}

.nav-link.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    .user-dropdown {
        background: #374151;
        border-color: #4b5563;
    }
    
    .dropdown-header {
        background: linear-gradient(135deg, #4b5563, #374151);
    }
    
    .dropdown-name {
        color: white;
    }
    
    .dropdown-email {
        color: #d1d5db;
    }
    
    .dropdown-item {
        color: #e5e7eb;
    }
    
    .dropdown-item:hover {
        background: #4b5563;
        color: #a855f7;
    }
    
    .dropdown-divider {
        background: #4b5563;
    }
}

/* Advanced animations */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.nav-link:hover::after {
    transform: translateX(100%);
}

/* Micro-interactions */
.subscription-badge:hover {
    transform: scale(1.05);
}

.notification-btn:active {
    transform: scale(0.95);
}

.user-avatar:hover {
    transform: scale(1.05);
}

/* Performance optimizations */
.modern-header {
    will-change: transform;
}

.nav-link,
.user-trigger,
.dropdown-item {
    will-change: transform, background-color;
}

/* Print styles */
@media print {
    .modern-header {
        display: none;
    }
}
</style>