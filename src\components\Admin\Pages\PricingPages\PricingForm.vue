<template>
    <form class="card" @submit.prevent="onSubmit">
      <div class="card-header bg-white py-3 d-flex justify-content-between sticky-top">
        <h4 class="m-0">
          <strong class="text-dark">{{ isEdit ? 'Edit Pricing Package' : 'Create Pricing Package' }}</strong>
        </h4>
        <div>
          <router-link class="btn btn-sm btn-secondary rounded-pill px-3 me-2" :to="{ name: 'AdminPricingList' }">Cancel</router-link>
          <button type="submit" class="btn btn-sm btn-primary rounded-pill px-3">Save Changes</button>
        </div>
      </div>
  
      <div class="card-body p-4">
        <div class="row g-3">
          <!-- General -->
          <BaseInput v-model="pricing.package_name" label="Package Name" type="text" placeholder="Pick a unique package name" wrapper-class="col-6" />
          <BaseInput v-model="pricing.package_level" label="Package Level" type="number" placeholder="Pick a pricing package level number" wrapper-class="col-6" />
          <BaseInput v-model="pricing.package_price" label="Package Price" type="number" wrapper-class="col-6" />
          <BaseInput v-model="pricing.currency" label="Currency" type="text" wrapper-class="col-6" />
  
          <!-- Risk Evaluation -->
          <SectionCard title="Risk Evaluation">
            <template #control><BaseCheckbox v-model="pricing.risk_evaluation.status" /></template>
            <BaseInput v-model="pricing.risk_evaluation.sessions" label="Sessions" type="number" :disabled="!pricing.risk_evaluation.status" />
            <BaseCheckbox v-model="pricing.risk_evaluation.includes_diagnosis" label="Includes Diagnosis" :disabled="!pricing.risk_evaluation.status" />
          </SectionCard>
  
          <!-- Fair Decision Analysis -->
          <SectionCard title="Fair Decision Analysis">
            <template #control><BaseCheckbox v-model="pricing.fair_decision_analysis.status" /></template>
            <BaseInput v-model="pricing.fair_decision_analysis.sessions" label="Sessions" type="number" :disabled="!pricing.fair_decision_analysis.status" />
            <BaseCheckbox v-model="pricing.fair_decision_analysis.includes_diagnosis" label="Includes Diagnosis" :disabled="!pricing.fair_decision_analysis.status" />
          </SectionCard>
  
          <!-- Training -->
          <SectionCard title="Training and Consultancy">
            <BaseCheckbox v-model="pricing.training" label="Training Included" />
            <BaseInput v-model="pricing.online_consultancy_hours" label="Online Consultancy Hours" type="number" />
          </SectionCard>
  
          <!-- In-Person Services -->
          <SectionCard title="In-Person Services">
            <template #control><BaseCheckbox v-model="pricing.in_person_services.status" /></template>
            <BaseCheckbox v-model="pricing.in_person_services.training" label="In-Person Training" :disabled="!pricing.in_person_services.status" />
            <BaseCheckbox v-model="pricing.in_person_services.consultancy" label="Consultancy" :disabled="!pricing.in_person_services.status" />
            <BaseInput v-model="pricing.in_person_services.consultancy_hours" label="Consultancy Hours" type="number" :disabled="!pricing.in_person_services.status || !pricing.in_person_services.consultancy" />
            <BaseCheckbox v-model="pricing.in_person_services.evaluation" label="Evaluation" :disabled="!pricing.in_person_services.status" />
            <BaseInput v-model="pricing.in_person_services.risk_evaluation_sessions" label="Risk Evaluation Sessions" type="number" :disabled="!pricing.in_person_services.status || !pricing.in_person_services.evaluation" />
            <BaseInput v-model="pricing.in_person_services.fair_decision_sessions" label="Fair Decision Sessions" type="number" :disabled="!pricing.in_person_services.status || !pricing.in_person_services.evaluation" />
          </SectionCard>
        </div>
      </div>
    </form>
</template>
  
<script setup>
    import BaseInput from '@/components/General/BaseInput.vue';
    import BaseCheckbox from '@/components/General/BaseCheckbox.vue';
    import SectionCard from '@/components/General/SectionCard.vue';
    defineProps(['pricing', 'isEdit', 'onSubmit']);
</script>