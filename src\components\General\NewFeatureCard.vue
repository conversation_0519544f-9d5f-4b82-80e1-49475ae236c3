<template>
  <div class="new-feature-card">
    <router-link :to="route" class="card-link">
      <div class="card-content">
        <div class="new-feature-icon" :style="iconStyle"> <i :class="icon"></i> </div>
        <h3 class="card-title">{{ title }}</h3>
        <p class="card-description">{{ description }}</p>
      </div>
      <div class="card-footer"> <span class="action-text">{{ actionText }}</span> <i :class="actionIcon"></i> </div>
    </router-link>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: { type: String, required: true },
  description: { type: String, required: true },
  icon: { type: String, default: 'fa fa-plus' },
  route: { type: Object, required: true },
  actionText: { type: String, default: 'Browse' },
  actionIcon: { type: String, default: 'fa fa-arrow-right' },
  primaryColor: { type: String, default: '#059669' },
  secondaryColor: { type: String, default: '#047857' }
});

const iconStyle = computed(() => ({
  background: `linear-gradient(135deg, ${props.primaryColor}, ${props.secondaryColor})`
}));
</script>

<style scoped>
@import '@/assets/scss/general/NewFeatureCard.scss';
</style>
