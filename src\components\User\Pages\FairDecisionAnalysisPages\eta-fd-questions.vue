<template>
    <form class="w-100" id="SubmitQuestions" @submit.prevent="SubmitQuestions">
        <div class="card shadow">
            <div class="card-header bg-white py-3">
                <h3 class="text-center text-secondary m-0"><strong>Fair Decision Analysis</strong></h3>
            </div>
            <div class="card-body bg-light py-3 px-4">

                <!-- Sector Info -->
                <div class="w-100 text-center mb-3" v-if="sub_sector_info != null">
                    <p class="btn btn-primary py-2 px-5 rounded-pill d-inline-block m-0 mb-2">
                        <strong>{{ sub_sector_info.title }}</strong>
                    </p>
                </div>

                <div class="w-100 mb-3" v-if="questions.length > 0">
                    <h3 class="py-3"><strong>Have you checked/evaluated and addressed the following:</strong></h3>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="w-100 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading questions...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading domain-specific fairness questions...</p>
                </div>

                <!-- Questions -->
                <div class="w-100 mb-3" v-if="questions.length > 0">
                    <div class="eachQuestions w-100 bg-white rounded shadow mb-4 p-4" v-for="(q, i) in questions" :key="q._id">
                        <div class="w-100">
                            <h5 class="m-0 p-0 mt-2">
                                <strong>{{ i + 1 }}. <span v-html="q.question"></span></strong>
                                <a href="javascript:void(0)" class="text-primary ms-2" v-if="q.question_hints !== ''"
                                   data-bs-container="body"
                                   data-bs-toggle="popover"
                                   data-bs-placement="top"
                                   :title="q.question"
                                   :data-bs-content="q.question_hints">
                                    <i class="fa fa-fw fa-info-circle"></i>
                                </a>
                            </h5>
                        </div>
                        <div class="w-100">
                            <div class="row">
                                <div class="col-xl-12 mt-3">
                                    <div class="w-100">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <ul class="list-unstyled px-2 px-lg-5">
                                                    <!-- Option 1 -->
                                                    <li class="mt-3" v-if="q.option_1.title !== ''">
                                                        <p class="text-dark m-0 p-0"><span v-html="q.option_1.title"></span></p>
                                                        <div class="w-100 mt-2 custom-radio-checkbox">
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q1'" type="radio" :id="'qCheck1Y'+i" value="0" required>
                                                                <label class="form-check-label" :for="'qCheck1Y'+i">Yes</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q1'" type="radio" :id="'qCheck1N'+i" :value="q.option_1.risk_value" required>
                                                                <label class="form-check-label" :for="'qCheck1N'+i">No</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom" :name="q._id+'_q1'" type="radio" :id="'qCheck1NA'+i" value="-1" required>
                                                                <label class="form-check-label" :for="'qCheck1NA'+i">NA</label>
                                                            </div>
                                                        </div>
                                                    </li>

                                                    <!-- Option 2 -->
                                                    <li class="mt-3" v-if="q.option_2.title !== ''">
                                                        <p class="text-dark m-0 p-0"><span v-html="q.option_2.title"></span></p>
                                                        <div class="w-100 mt-2 custom-radio-checkbox">
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q2'" type="radio" :id="'qCheck2Y'+i" value="0" required>
                                                                <label class="form-check-label" :for="'qCheck2Y'+i">Yes</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q2'" type="radio" :id="'qCheck2N'+i" :value="q.option_2.risk_value" required>
                                                                <label class="form-check-label" :for="'qCheck2N'+i">No</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom" :name="q._id+'_q2'" type="radio" :id="'qCheck2NA'+i" value="-1" required>
                                                                <label class="form-check-label" :for="'qCheck2NA'+i">NA</label>
                                                            </div>
                                                        </div>
                                                    </li>

                                                    <!-- Option 3 -->
                                                    <li class="mt-3" v-if="q.option_3 && q.option_3.title !== ''">
                                                        <p class="text-dark m-0 p-0"><span v-html="q.option_3.title"></span></p>
                                                        <div class="w-100 mt-2 custom-radio-checkbox">
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q3'" type="radio" :id="'qCheck3Y'+i" value="0" required>
                                                                <label class="form-check-label" :for="'qCheck3Y'+i">Yes</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q3'" type="radio" :id="'qCheck3N'+i" :value="q.option_3.risk_value" required>
                                                                <label class="form-check-label" :for="'qCheck3N'+i">No</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom" :name="q._id+'_q3'" type="radio" :id="'qCheck3NA'+i" value="-1" required>
                                                                <label class="form-check-label" :for="'qCheck3NA'+i">NA</label>
                                                            </div>
                                                        </div>
                                                    </li>

                                                    <!-- Option 4 -->
                                                    <li class="mt-3" v-if="q.option_4 && q.option_4.title !== ''">
                                                        <p class="text-dark m-0 p-0"><span v-html="q.option_4.title"></span></p>
                                                        <div class="w-100 mt-2 custom-radio-checkbox">
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q4'" type="radio" :id="'qCheck4Y'+i" value="0" required>
                                                                <label class="form-check-label" :for="'qCheck4Y'+i">Yes</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom"
                                                                       :name="q._id+'_q4'" type="radio" :id="'qCheck4N'+i" :value="q.option_4.risk_value" required>
                                                                <label class="form-check-label" :for="'qCheck4N'+i">No</label>
                                                            </div>
                                                            <div class="form-check form-check-inline ps-0">
                                                                <input class="form-check-input-custom" :name="q._id+'_q4'" type="radio" :id="'qCheck4NA'+i" value="-1" required>
                                                                <label class="form-check-label" :for="'qCheck4NA'+i">NA</label>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Questions Message -->
                <div v-if="questions.length === 0 && !loading" class="w-100 text-center py-5">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle fa-2x mb-3"></i>
                        <h4>No Domain-Specific Questions Found</h4>
                        <p>There are currently no questions available for this domain. Please contact the administrator.</p>
                    </div>
                </div>

            </div>
            <div class="card-footer py-3 d-flex justify-content-between">
                <button @click="goBack" type="button" class="btn btn-outline-secondary px-5">
                    <i class="fa fa-fw fa-arrow-left"></i> Back
                </button>
                <button type="submit" class="btn btn-primary px-5" :disabled="questions.length === 0">
                    Next <i class="fa fa-fw fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </form>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';
import Swal from "sweetalert2";

export default {
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            domain: '',
            fdSector: null,
            sub_sector: '',
            sub_sector_index: 0,
            sub_sector_info: null,
            questions: [],
            risk_evaluation: null,
            risk_analysis: 'fair',
            fairnessAnalysisActions: null
        }
    },
    async created() {
        // Initialize fairness analysis actions
        this.fairnessAnalysisActions = useFairnessAnalysisActions();
        
        // Get domain from route parameters
        this.domain = this.$route.params.domain;
        
        // Set analysis type to fairness
        localStorage.setItem('risk_analysis', 'fair');
        
        // Load evaluation data and set category
        if (localStorage.getItem('risk_evaluation') == null) {
            this.$router.push({name: 'StartFairDecisionAnalysis'});
        } else {
            this.risk_evaluation = JSON.parse(localStorage.getItem('risk_evaluation'));
            // CRITICAL: Set the correct category for backend
            this.risk_evaluation.category = 'eta-fd';
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
        }
        
        // Load sector details and questions
        this.fdSectors();
    },
    mounted() {
        window.scrollTo(0, 0);
    },
    methods: {
getQuestions() {
    let THIS = this;
    this.questions.length = 0;
    
    // Call backend with proper parameters for industry-specific fairness questions
    const requestData = {
        sector: THIS.domain,
        sub_sector: THIS.sub_sector,
        category: this.risk_evaluation.category // 'eta-fd'
    };
    
    ApiService.POST(ApiRoutes.EvaluationTechnicalQuestions, requestData, (res) => {
        if (parseInt(res.status) === 200) {
            THIS.questions = res.data;
            window.scrollTo(0, 0);

            setTimeout(function () {
                const ptl = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
                ptl.map(function (el) {
                    return new bootstrap.Popover(el, {trigger: 'hover'})
                })
            }, 1000);
        } else {
            console.error('Failed to load industry fairness questions:', res);
        }
    });
},
        async SubmitQuestions() {
            // Check eligibility before submitting
            const canSubmit = await this.checkFairnessEligibility();
            if (!canSubmit) {
                return;
            }

            let THIS = this;
            const object = {};
            const formData = new FormData(document.getElementById('SubmitQuestions'));
            formData.forEach(function (value, key) {
                object[key] = value;
            });
            
            // Store answers with correct key structure for industry-specific fairness
            const answerKey = THIS.sub_sector 
                ? `domain_${THIS.domain}_${THIS.sub_sector}`
                : `domain_${THIS.domain}`;
            
            THIS.risk_evaluation.answers[answerKey] = object;
            localStorage.setItem('risk_evaluation', JSON.stringify(THIS.risk_evaluation));

            // Check if there are more sub-sectors to process
            if((this.sub_sector_index + 1) < this.fdSector.sub_sectors.length){
                this.sub_sector_index = this.sub_sector_index + 1;
                this.sub_sector = this.fdSector.sub_sectors[this.sub_sector_index].uid;
                this.sub_sector_info = this.fdSector.sub_sectors[this.sub_sector_index];
                this.getQuestions();
            } else {
                // All sub-sectors completed, go to completion page
                THIS.$router.push({name: 'FairnessAlmostDone'})
            }
        },
        
        fdSectors() {
            // Load fairness sector details
            ApiService.POST(ApiRoutes.FdSectorDetails, {uid: this.domain}, (res) => {
                if (parseInt(res.status) === 200) {
                    this.fdSector = res.data;
                    if(this.sub_sector === '' && this.fdSector.sub_sectors.length > 0){
                        this.sub_sector_index = 0;
                        this.sub_sector = this.fdSector.sub_sectors[this.sub_sector_index].uid;
                        this.sub_sector_info = this.fdSector.sub_sectors[this.sub_sector_index];
                    }
                    this.getQuestions();
                } else {
                    console.error('Failed to load fairness sector details:', res);
                }
            })
        },
        
        goBack() {
            this.$router.push({name: 'FairnessDomains'})
        },
        
        async checkFairnessEligibility() {
            try {
                const remaining = await this.fairnessAnalysisActions.countRemainingFairnessAnalysisEvaluations();
                
                if (remaining <= 0) {
                    await Swal.fire({
                        icon: 'warning',
                        title: 'No Fairness Analysis Left',
                        text: 'You have no fairness analysis remaining. Please purchase a package.',
                        confirmButtonText: 'Go to Pricing',
                    });
                    this.$router.push({ name: 'Pricing' });
                    return false;
                }
                return true;
            } catch (error) {
                console.error('Failed to check fairness eligibility:', error);
                return true; // Allow on error
            }
        }
    }
}
</script>

<style scoped>
/* Custom radio button styling for fairness analysis */
.custom-radio-checkbox .form-check-input-custom {
    margin-right: 0.5rem;
}

.custom-radio-checkbox .form-check-label {
    margin-right: 1rem;
    font-weight: 500;
}

.eachQuestions {
    transition: all 0.3s ease;
    border-left: 4px solid #7c3aed;
}

.eachQuestions:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    color: white;
}

.btn-primary {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5b21b6, #4c1d95);
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}
</style>