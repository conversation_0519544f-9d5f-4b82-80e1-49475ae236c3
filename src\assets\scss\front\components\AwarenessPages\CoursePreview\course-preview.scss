.modern-course-container {
    min-height: 100vh;
    background: #f8fafc;
}
.course-main-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
    align-items: start;
}
.course-content {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    min-height: 600px;
    overflow: hidden;
}
.course-overview {
    padding: 2rem;
}
.overview-card {
    max-width: 800px;
    margin: 0 auto;
}
.overview-header {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    align-items: center;
}
.course-info {
    flex: 1;
}
.overview-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}
.overview-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}
.lesson-content {
    height: 100%;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .course-main-content {
        grid-template-columns: 300px 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .course-main-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    .course-content {
        order: 1;
    }
}

@media (max-width: 768px) {
    .modern-course-container {
        padding: 0;
    }
    .course-overview {
        padding: 1rem;
    }
    .overview-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    .course-image {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }
    .overview-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .overview-title {
        font-size: 1.25rem;
    }
}

/* Enhanced Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-content {
    animation: slideInRight 0.6s ease-out 0.2s both;
}

/* Focus States for Accessibility */
.start-learning-btn:focus,
.continue-btn:focus,
.certificate-btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .topic-header,
    .lesson-link,
    .course-sidebar,
    .course-content {
        border-width: 2px;
    }
    .topic-icon,
    .lesson-status i {
        filter: contrast(1.2);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .course-sidebar,
    .course-content,
    .topic-item {
        animation: none;
    }
    .expand-icon,
    .circle-progress,
    .topic-header,
    .lesson-link {
        transition: none;
    }
    @keyframes ring {
        from, to {
            transform: rotate(0deg);
        }
    }
}

/* Print Styles */
@media print {
    .course-header {
        background: white !important;
        color: black !important;
    }
    .course-sidebar {
        display: none;
    }
    .course-main-content {
        grid-template-columns: 1fr;
    }
    .start-learning-btn,
    .continue-btn,
    .certificate-btn {
        background: white !important;
        color: black !important;
        border: 1px solid black;
    }
}
.course-image {
    width: 200px;
    height: 120px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.course-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
