<template>
  <div class="container-lg py-4">
    <PricingForm :pricing="pricing" :isEdit="false" :onSubmit="() => createPricing(pricing)" />
  </div>
</template>

<script setup>
import { usePricingFormState } from '@/composables/pricing/usePricingFormState';
import { usePricingActions } from '@/composables/pricing/usePricingActions';
import PricingForm from '@/components/Admin/Pages/PricingPages/PricingForm.vue';

const { pricing } = usePricingFormState();
const { createPricing } = usePricingActions();
</script>
