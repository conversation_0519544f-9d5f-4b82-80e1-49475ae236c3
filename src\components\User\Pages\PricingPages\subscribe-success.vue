<template>
  <div class="subscribe-success min-vh-100 d-flex justify-content-center align-items-center">
    <div v-if="loading" class="text-center">
      <p class="fs-4">Saving your subscription...</p>
    </div>
    <div v-else-if="error" class="text-center text-danger">
      <p class="fs-4">{{ error }}</p>
    </div>
    <div v-else class="text-center text-success">
      <p class="fs-4">Subscription successfully saved!</p>
      <p>Redirecting to your profile...</p>
    </div>
  </div>
</template>
  
<script setup>
import { ref, onMounted } from 'vue';
import { usePricingActions } from '@/composables/pricing/usePricingActions';


const { handleSubscriptionSuccess, error } = usePricingActions();
const loading = ref(true);

onMounted(async () => {
  await handleSubscriptionSuccess();
  loading.value = false;
});
</script>
  