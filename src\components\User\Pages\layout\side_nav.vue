<template>
    <ul class="m-0 p-0 list-unstyled">
        <li>
            <a class="w-100 mb-2 d-inline-block text-end text-dark">
                <span><i class="fa fa-2x fa-fw fa-bars"></i></span>
            </a>
        </li>
        <li>
            <router-link :to="{name: 'Dashboard'}" class="w-100 shadow-sm p-3 border rounded-3 text-decoration-none d-block text-center mb-2  fw-bold"
                         :class="{'bg-warning text-dark': current_menu === 'Dashboard', 'bg-white text-secondary': current_menu !== 'Dashboard'}"> Dashboard </router-link>
        </li>
        <li>
            <router-link :to="{name: 'RiskEvaluation'}"  class="w-100 shadow-sm p-3 border rounded-3 text-decoration-none d-block text-center mb-2  fw-bold"
               :class="{'bg-warning text-dark': current_menu === 'RiskEvaluation', 'bg-white text-secondary': current_menu !== 'RiskEvaluation'}"> Risk Evaluation </router-link>
        </li>
        <li>
            <router-link :to="{name: 'FairDecisionAnalysis'}"  class="w-100 shadow-sm p-3 border rounded-3 text-decoration-none d-block text-center mb-2  fw-bold"
               :class="{'bg-warning text-dark': current_menu === 'FairnessAnalysis', 'bg-white text-secondary': current_menu !== 'FairnessAnalysis'}"> Fairness Analysis </router-link>
        </li>
        <li>
            <router-link :to="{name: 'AwarenessEvaluation'}"  class="w-100 shadow-sm p-3 border rounded-3 text-decoration-none d-block text-center mb-2  fw-bold"
                         :class="{'bg-warning text-dark': current_menu === 'AwarenessEvaluation', 'bg-white text-secondary': current_menu !== 'AwarenessEvaluation'}"> Awareness </router-link>
        </li>
        <li>
            <a href="javascript:void(0)"  class="w-100 shadow-sm p-3 border rounded-3 text-decoration-none d-block text-center mb-2  fw-bold"
               :class="{'bg-warning text-dark': current_menu === 'Certification', 'bg-white text-secondary': current_menu !== 'Certification'}"> Certification </a>
        </li>
        <li>
            <a href="javascript:void(0)"  class="w-100 shadow-sm p-3 border rounded-3 text-decoration-none d-block text-center mb-2  fw-bold"
               :class="{'bg-warning text-dark': current_menu === 'Consultancy', 'bg-white text-secondary': current_menu !== 'Consultancy'}"> Consultancy </a>
        </li>
    </ul>
</template>
<script>
export default {
    props: {
        current_menu: {
            type: String,
            required: true
        }
    },
    data() {
        return {
        }
    },
    computed: {},
    watch: {},
    methods: {
    },
    created() {
    },
    mounted() {
    },
}
</script>
