<template>
    <div class="modern-profile-update">
        <!-- <PERSON> Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">
                        <i class="fa fa-edit title-icon"></i>
                        Update Profile
                    </h1>
                    <p class="page-subtitle">
                        Edit your personal information and account settings
                    </p>
                </div>
                <div class="header-actions">
                    <router-link :to="{ name: 'Profile' }" class="btn-secondary">
                        <i class="fa fa-arrow-left"></i>
                        Back to Profile
                    </router-link>
                </div>
            </div>
        </div>

        <!-- Update Form -->
        <div class="update-content">
            <div class="update-card">
                <form id="profileUpdate" @submit.prevent="updateProfile" enctype="multipart/form-data">
                    
                    <!-- Avatar Section -->
                    <div class="avatar-section">
                        <div class="avatar-header">
                            <div class="section-title">
                                <i class="fa fa-camera section-icon"></i>
                                Profile Picture
                            </div>
                        </div>
                        
                        <div class="avatar-content">
                            <div class="avatar-upload-container">
                                <div class="current-avatar">
                                    <img 
                                        class="profile-avatar" 
                                        :src="UserInfo.avatar == null ? '/img/user.png' : `${BACKEND_BASE_URL}/storage/media/image/${UserInfo.avatar}`" 
                                        alt="Profile Avatar"
                                    >
                                    <div class="avatar-overlay">
                                        <i class="fa fa-camera"></i>
                                    </div>
                                </div>
                                
                                <div class="avatar-actions">
                                    <label for="changeProfileAvatar" class="upload-btn">
                                        <i class="fa fa-upload"></i>
                                        Change Picture
                                    </label>
                                    <input type="file" class="d-none" id="changeProfileAvatar" name="file" accept="image/*">
                                    <p class="upload-note">
                                        Recommended: Square image, at least 200x200px
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fa fa-user section-icon"></i>
                                Personal Information
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fa fa-user"></i>
                                    First Name
                                </label>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    :value="UserInfo.first_name" 
                                    name="first_name" 
                                    placeholder="Enter your first name"
                                    required
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fa fa-user"></i>
                                    Last Name
                                </label>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    :value="UserInfo.last_name" 
                                    name="last_name" 
                                    placeholder="Enter your last name"
                                    required
                                >
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fa fa-envelope section-icon"></i>
                                Contact Information
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label class="form-label">
                                    <i class="fa fa-envelope"></i>
                                    Email Address
                                </label>
                                <input 
                                    type="email" 
                                    class="form-control" 
                                    :value="UserInfo.email" 
                                    name="email" 
                                    placeholder="Enter your email address"
                                    required
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fa fa-phone"></i>
                                    Phone Number
                                </label>
                                <input 
                                    type="tel" 
                                    class="form-control" 
                                    :value="UserInfo.phone" 
                                    name="phone" 
                                    placeholder="Enter your phone number"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- Professional Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fa fa-briefcase section-icon"></i>
                                Professional Information
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fa fa-building"></i>
                                    Company
                                </label>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    :value="UserInfo.company" 
                                    name="company" 
                                    placeholder="Enter your company name"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fa fa-globe"></i>
                                    Website
                                </label>
                                <input 
                                    type="url" 
                                    class="form-control" 
                                    :value="UserInfo.website" 
                                    name="website" 
                                    placeholder="https://yourwebsite.com"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <router-link :to="{ name: 'Profile' }" class="btn-cancel">
                            <i class="fa fa-times"></i>
                            Cancel
                        </router-link>
                        <button type="submit" class="btn-save">
                            <i class="fa fa-save"></i>
                            Save Changes
                        </button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { createToaster } from "@meforma/vue-toaster";
const Toaster = createToaster({position: 'bottom-right'});

export default {
    name: 'ModernProfileUpdate',
    data() {
        return {
            loading: 1,
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            BACKEND_BASE_URL: import.meta.env.VITE_API_URL
        }
    },
    computed: {},
    watch: {},
    methods: {
        updateProfile: function() {
            const THIS = this;
            const formData = new FormData(document.getElementById('profileUpdate'));
            ApiService.POST_FORM_DATA(ApiRoutes.ProfileUpdate, formData, function(res) {
                if (res.status === 200) {
                    localStorage.setItem('UserInfo', JSON.stringify(res.user));
                    Toaster.success(res.msg);
                    THIS.$router.push({name: 'Profile'});
                }
            })
        }
    },
    created() {},
    mounted() {
        window.scrollTo(0, 0);
    },
}
</script>

<style scoped>
.modern-profile-update {
    max-width: 1000px;
    margin: 0 auto;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

/* Update Content */
.update-content {
    display: flex;
    justify-content: center;
}

.update-card {
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    width: 100%;
    max-width: 800px;
}

/* Avatar Section */
.avatar-section {
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, rgba(121, 37, 199, 0.05), rgba(168, 85, 247, 0.05));
}

.avatar-header {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-icon {
    width: 20px;
    color: #7925c7;
}

.avatar-content {
    display: flex;
    justify-content: center;
}

.avatar-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.current-avatar {
    position: relative;
    cursor: pointer;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: rgba(121, 37, 199, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
    font-size: 1.5rem;
}

.current-avatar:hover .avatar-overlay {
    opacity: 1;
}

.avatar-actions {
    text-align: center;
}

.upload-btn {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.upload-btn:hover {
    background: linear-gradient(135deg, #5b21b6, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(121, 37, 199, 0.3);
}

.upload-note {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0;
}

/* Form Sections */
.form-section {
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.form-section:last-of-type {
    border-bottom: none;
}

.section-header {
    margin-bottom: 1.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label i {
    width: 16px;
    color: #7925c7;
}

.form-control {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #7925c7;
    background: white;
    box-shadow: 0 0 0 3px rgba(121, 37, 199, 0.1);
}

.form-control::placeholder {
    color: #9ca3af;
}

/* Form Actions */
.form-actions {
    padding: 2rem;
    background: #f8fafc;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-cancel {
    background: #f3f4f6;
    color: #374151;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-cancel:hover {
    background: #e5e7eb;
    color: #1f2937;
    text-decoration: none;
    border-color: #d1d5db;
}

.btn-save {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-save:hover {
    background: linear-gradient(135deg, #5b21b6, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(121, 37, 199, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 1.875rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-section {
        padding: 1.5rem;
    }
    
    .avatar-section {
        padding: 1.5rem;
    }
    
    .form-actions {
        padding: 1.5rem;
        flex-direction: column;
    }
    
    .form-actions .btn-cancel,
    .form-actions .btn-save {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .profile-avatar {
        width: 100px;
        height: 100px;
    }
    
    .form-section {
        padding: 1rem;
    }
    
    .avatar-section {
        padding: 1rem;
    }
    
    .form-actions {
        padding: 1rem;
    }
}

/* Focus states for accessibility */
.upload-btn:focus,
.btn-cancel:focus,
.btn-save:focus {
    outline: 2px solid #7925c7;
    outline-offset: 2px;
}

/* Enhanced animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-section {
    animation: slideInUp 0.6s ease-out;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .form-section,
    .btn-secondary,
    .btn-cancel,
    .btn-save,
    .upload-btn,
    .form-control {
        transition: none;
        animation: none;
    }
    
    @keyframes slideInUp {
        from, to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* Hidden class for file input */
.d-none {
    display: none !important;
}
</style>