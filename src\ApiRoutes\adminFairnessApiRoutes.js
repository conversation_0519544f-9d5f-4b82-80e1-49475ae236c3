const BASE_URL = import.meta.env.VITE_API_URL;
const ApiUrl = `${BASE_URL}/api/secure/admin`;

const AdminFairnessApiRoutes = {
    // Fair Decision Analysis Dashboard routes
    FairDecisionDomainsCharts: ApiUrl + "/dashboard/report/fair-decision",
    FairnessAnalysisCharts: ApiUrl + "/dashboard/report/fairness-analysis",
    FairnessUsageCharts: ApiUrl + "/dashboard/report/fairness-usage",
    
    // Fairness Sector Management
    FdSectorSectorGetAll: ApiUrl + "/evaluation/fd/sector/get/all",
    FdSectorSectorManage: ApiUrl + "/evaluation/fd/sector/manage",
    FairnessSectorGetAll: ApiUrl + "/fairness/sector/get/all",
    FairnessSectorManage: ApiUrl + "/fairness/sector/manage",
    
    // Fairness Question Group Management
    FairnessQuestionGroupsGetAll: ApiUrl + "/fairness/question/groups/get/all",
    FairnessQuestionGroupsManage: ApiUrl + "/fairness/question/groups/manage",
    
    // Fairness Question Management
    ListFairnessQuestions: ApiUrl + "/fairness/question/list",
    ManageFairnessQuestion: ApiUrl + "/fairness/question/manage",
    DeleteFairnessQuestion: ApiUrl + "/fairness/question/delete",
    FairnessQuestionTypes: ApiUrl + "/fairness/question/types",
    
    // Fairness Factor Management (if different from risk factors)
    FairnessFactorGetAll: ApiUrl + "/fairness/factor/get/all",
    FairnessFactorManage: ApiUrl + "/fairness/factor/manage",
    
    // Fairness Evaluation Management
    FairnessEvaluations: ApiUrl + "/fairness/evaluation/list",
    FairnessEvaluationSingle: (evaluation_id) => `${ApiUrl}/fairness/evaluation/single/${evaluation_id}`,
    FairnessEvaluationDelete: (evaluation_id) => `${ApiUrl}/fairness/evaluation/delete/${evaluation_id}`,
    FairnessEvaluationUpdate: (evaluation_id) => `${ApiUrl}/fairness/evaluation/update/${evaluation_id}`,
    
    // Fairness Certification routes
    FairnessEvaluationCertificates: ApiUrl + "/fairness/certificate/list",
    FairnessEvaluationCertificateSettings: ApiUrl + "/fairness/certificate/settings",
    FairnessEvaluationCertificateSettingsUpdate: ApiUrl + "/fairness/certificate/settings/update",
    FairnessParticipantCertificates: ApiUrl + "/fairness/participant/certificate/list",
    
    // Fairness Analytics
    FairnessAnalyticsOverview: ApiUrl + "/fairness/analytics/overview",
    FairnessAnalyticsSectors: ApiUrl + "/fairness/analytics/sectors",
    FairnessAnalyticsUsers: ApiUrl + "/fairness/analytics/users",
    FairnessAnalyticsReports: ApiUrl + "/fairness/analytics/reports",
};

export default AdminFairnessApiRoutes;