.dash-ai-tools{
    .analysis_et_ribbon{
        display: inline-block;
        position: absolute;
        top: 17px;
        left: -54px;
        background: #0E524A;
        color: #ffffff;
        padding: 15px 50px;
        font-size: 13px;
        transform: rotate(-45deg);
        font-weight: bold;
        border: 2px solid #0a3832;
        z-index: 3;
        &.analysis_fd_ribbon{
            top: 15px;
            left: -48px;
            background: rgba(78, 80, 253, 0.74);
            border: 2px solid rgba(78, 80, 253, 1);
        }
    }
    .each-ai-tools{
        width: 100%;
        height: 350px;
        text-decoration: none;
        background: #f5f5f5;
        transform: scale(1);
        @include transition(all 0.2s linear);
        &:hover,&:active{
            background: #e6e6e6;
            text-decoration: none;
            transform: scale(1.1);
            @include transition(all 0.2s linear);
        }
        .ai-tool-create{
            height: calc(350px - 20px);
        }
    }
}
.table-text-small{
    th,td{
        font-size: 14px;
    }
}


.form-group{
    position: relative;
    .title_count{
        position: absolute;
        top: 43px;
        right: 20px;
        opacity: 0.5;
        @media (max-width: 767px) {
            display: none;
        }
    }
}

.eachPrice{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 550px;
    overflow: hidden;
    border-radius: 15px;
    position: relative;
    .active_status{
        position: absolute;
        top: 27px;
        right: -50px;
        background: var(--bs-success);
        padding: 10px 50px;
        color: var(--bs-white);
        transform: rotate(45deg);
        box-shadow: 0px 3px 7px #777777;
        font-weight: bold;
        &.canceled_status{
            top: 15px;
            right: -45px;
            background: var(--bs-danger);
        }
    }
    .plan{
        font-size: 25px;
        font-weight: 600;
    }
    .price{
        font-size: 40px;
        font-weight: 800;
        small{
            display: inline-block;
            font-size: 15px!important;
            font-weight: 600;
            vertical-align: middle;
            opacity: 0.7;
        }
    }
    .short_details{
        font-size: 17px;
        display: block;
    }
    .features{
        font-size: 14px;
        display: block;
        ul{
            list-style: none;
            margin: 0;
            padding: 0;
        }
    }
}
