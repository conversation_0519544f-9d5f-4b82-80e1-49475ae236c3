<!-- src/components/User/Pages/PortalPages/profile.vue -->
<template>
    <div class="modern-profile">
        <!-- Page Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">
                        <i class="fa fa-user title-icon"></i>
                        My Profile
                    </h1>
                    <p class="page-subtitle">
                        Manage your account settings and view your information
                    </p>
                </div>
                <div class="header-actions">
                    <router-link :to="{ name: 'ProfileUpdate' }" class="btn-primary">
                        <i class="fa fa-edit"></i>
                        Edit Profile
                    </router-link>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="profile-content">
            <!-- Profile Overview Card -->
            <div class="profile-overview-card">
                <div class="card-background">
                    <div class="background-pattern"></div>
                </div>
                
                <div class="profile-header">
                    <div class="profile-avatar-section">
                        <div class="avatar-container">
                            <img 
                                :src="getUserAvatar()" 
                                :alt="UserInfo.name || 'User'"
                                class="profile-avatar"
                                @error="handleAvatarError"
                            >
                            <div class="avatar-overlay">
                                <router-link 
                                    :to="{ name: 'ProfileUpdate' }" 
                                    class="avatar-edit-btn"
                                    title="Edit Profile Picture"
                                >
                                    <i class="fa fa-camera"></i>
                                </router-link>
                            </div>
                        </div>
                        
                        <div class="profile-info">
                            <h2 class="profile-name">{{ UserInfo.name || 'User' }}</h2>
                            <p class="profile-email">{{ UserInfo.email || '' }}</p>
                            <div class="profile-badges">
                                <span 
                                    class="subscription-badge" 
                                    :class="subscriptionBadgeClass"
                                >
                                    <i class="fa fa-star" v-if="Subscription.package_price !== 0"></i>
                                    {{ Subscription.package_name || 'Free Plan' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Horizontal - Now in Profile Overview Card -->
                    <div class="profile-quick-actions">
                        <h3 class="quick-actions-title">Quick Actions</h3>
                        <div class="horizontal-action-grid">
                            <router-link :to="{ name: 'ProfileUpdate' }" class="horizontal-action-item">
                                <div class="horizontal-action-icon">
                                    <i class="fa fa-edit"></i>
                                </div>
                                <div class="horizontal-action-text">
                                    <div class="horizontal-action-title">Update Profile</div>
                                    <div class="horizontal-action-description">Edit personal info</div>
                                </div>
                            </router-link>
                            
                            <router-link :to="{ name: 'ProfilePasswordUpdate' }" class="horizontal-action-item">
                                <div class="horizontal-action-icon">
                                    <i class="fa fa-key"></i>
                                </div>
                                <div class="horizontal-action-text">
                                    <div class="horizontal-action-title">Change Password</div>
                                    <div class="horizontal-action-description">Update password</div>
                                </div>
                            </router-link>
                            
                            <router-link :to="{ name: 'Pricing' }" class="horizontal-action-item">
                                <div class="horizontal-action-icon">
                                    <i class="fa fa-star"></i>
                                </div>
                                <div class="horizontal-action-text">
                                    <div class="horizontal-action-title">{{ Subscription.package_price === 0 ? 'Upgrade Plan' : 'Manage Plan' }}</div>
                                    <div class="horizontal-action-description">{{ Subscription.package_price === 0 ? 'Get premium' : 'View details' }}</div>
                                </div>
                            </router-link>
                            
                            <router-link :to="{ name: 'Dashboard' }" class="horizontal-action-item">
                                <div class="horizontal-action-icon">
                                    <i class="fa fa-tachometer"></i>
                                </div>
                                <div class="horizontal-action-text">
                                    <div class="horizontal-action-title">Dashboard</div>
                                    <div class="horizontal-action-description">Back to main</div>
                                </div>
                            </router-link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details Grid -->
            <div class="profile-details-grid">
                <!-- Personal Information Card -->
                <div class="detail-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fa fa-user card-icon"></i>
                            Personal Information
                        </div>
                        <router-link 
                            :to="{ name: 'ProfileUpdate' }" 
                            class="edit-link"
                        >
                            <i class="fa fa-edit"></i>
                            Edit
                        </router-link>
                    </div>
                    
                    <div class="card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fa fa-user"></i>
                                    Full Name
                                </div>
                                <div class="info-value">{{ UserInfo.name || 'Not provided' }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fa fa-envelope"></i>
                                    Email Address
                                </div>
                                <div class="info-value">{{ UserInfo.email || 'Not provided' }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fa fa-phone"></i>
                                    Phone Number
                                </div>
                                <div class="info-value">{{ UserInfo.phone || 'Not provided' }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fa fa-building"></i>
                                    Company
                                </div>
                                <div class="info-value">{{ UserInfo.company || 'Not provided' }}</div>
                            </div>
                            
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fa fa-globe"></i>
                                    Website
                                </div>
                                <div class="info-value">
                                    <a 
                                        v-if="UserInfo.website" 
                                        :href="formatWebsiteUrl(UserInfo.website)"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="website-link"
                                    >
                                        {{ UserInfo.website }}
                                        <i class="fa fa-external-link"></i>
                                    </a>
                                    <span v-else>Not provided</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Information Card -->
                <div class="detail-card subscription-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fa fa-star card-icon"></i>
                            Current Package
                        </div>
                        <router-link 
                            :to="{ name: 'Pricing' }" 
                            class="upgrade-link"
                        >
                            <i class="fa fa-arrow-up"></i>
                            {{ Subscription.package_price === 0 ? 'Upgrade' : 'Manage Plan' }}
                        </router-link>
                    </div>
                    
                    <div class="card-content">
                        <div class="subscription-status">
                            <div class="status-indicator" :class="subscriptionStatusClass">
                                <div class="status-icon">
                                    <i class="fa fa-star" v-if="Subscription.package_price !== 0"></i>
                                    <i class="fa fa-user" v-else></i>
                                </div>
                                <div class="status-content">
                                    <div class="status-title">
                                        {{ Subscription.package_name || 'Free Plan' }}
                                    </div>
                                    <div class="status-price">
                                        {{ Subscription.package_price === 0 ? 'Free' : `${Subscription.currency || '$'} ${Subscription.package_price}` }}
                                    </div>
                                    <div class="status-description">
                                        {{ Subscription.package_price === 0 
                                            ? 'Basic features with limited access' 
                                            : 'Full access to all premium features' 
                                        }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="subscription-actions" v-if="Subscription.package_price === 0">
                                <router-link :to="{ name: 'Pricing' }" class="upgrade-btn">
                                    <i class="fa fa-star"></i>
                                    Upgrade to Premium
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Security Card -->
                <div class="detail-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fa fa-shield card-icon"></i>
                            Account Security
                        </div>
                        <router-link 
                            :to="{ name: 'ProfilePasswordUpdate' }" 
                            class="edit-link"
                        >
                            <i class="fa fa-key"></i>
                            Change Password
                        </router-link>
                    </div>
                    
                    <div class="card-content">
                        <div class="security-items">
                            <div class="security-item">
                                <div class="security-icon">
                                    <i class="fa fa-lock"></i>
                                </div>
                                <div class="security-content">
                                    <div class="security-title">Password Protection</div>
                                    <div class="security-description">Your account is protected with a secure password</div>
                                </div>
                                <div class="security-status">
                                    <span class="status-badge secure">Protected</span>
                                </div>
                            </div>
                            
                            <div class="security-item">
                                <div class="security-icon">
                                    <i class="fa fa-envelope"></i>
                                </div>
                                <div class="security-content">
                                    <div class="security-title">Email Verification</div>
                                    <div class="security-description">Your email address is verified and secure</div>
                                </div>
                                <div class="security-status">
                                    <span class="status-badge verified">Verified</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ModernProfile',
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            Subscription: JSON.parse(localStorage.getItem('Subscription')),
            BACKEND_BASE_URL: import.meta.env.VITE_API_URL
        }
    },
    computed: {
        subscriptionBadgeClass() {
            return this.Subscription.package_price !== 0 ? 'premium' : 'free';
        },
        
        subscriptionStatusClass() {
            return this.Subscription.package_price !== 0 ? 'status-premium' : 'status-free';
        }
    },
    methods: {
        getUserAvatar() {
            return this.UserInfo?.avatar 
                ? `${this.BACKEND_BASE_URL}/storage/media/image/${this.UserInfo.avatar}`
                : '/img/user.png';
        },
        
        handleAvatarError(event) {
            event.target.src = '/img/user.png';
        },
        
        formatWebsiteUrl(url) {
            if (!url) return '';
            return url.startsWith('http://') || url.startsWith('https://') 
                ? url 
                : `https://${url}`;
        }
    },
    mounted() {
        window.scrollTo(0, 0);
    }
}
</script>

<style scoped>
.modern-profile {
    max-width: 1200px;
    margin: 0 auto;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

/* Profile Content */
.profile-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Profile Overview Card */
.profile-overview-card {
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    overflow: hidden;
}

.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
}

.profile-header {
    position: relative;
    padding: 2rem;
    padding-top: 1rem;
}

.profile-avatar-section {
    display: flex;
    align-items: flex-end;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.avatar-container {
    position: relative;
    margin-top: -60px;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 6px solid white;
    object-fit: cover;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.avatar-edit-btn {
    color: white;
    text-decoration: none;
    font-size: 1.25rem;
    transition: transform 0.3s ease;
}

.avatar-edit-btn:hover {
    transform: scale(1.1);
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.profile-email {
    font-size: 1.125rem;
    color: #6b7280;
    margin: 0 0 1rem 0;
}

.profile-badges {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.subscription-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.subscription-badge.premium {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.subscription-badge.free {
    background: #64748b;
    color: white;
}

/* Horizontal Quick Actions in Profile Card */
.profile-quick-actions {
    border-top: 1px solid #e5e7eb;
    padding-top: 1.5rem;
}

.quick-actions-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-actions-title::before {
    content: '';
    width: 4px;
    height: 1.5rem;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    border-radius: 2px;
}

.horizontal-action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.horizontal-action-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.horizontal-action-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(121, 37, 199, 0.1), transparent);
    transition: left 0.5s ease;
}

.horizontal-action-item:hover::before {
    left: 100%;
}

.horizontal-action-item:hover {
    background: #f1f5f9;
    border-color: #7925c7;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    color: inherit;
    text-decoration: none;
}

.horizontal-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.horizontal-action-text {
    flex: 1;
    min-width: 0;
    position: relative;
    z-index: 1;
}

.horizontal-action-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.horizontal-action-description {
    font-size: 0.75rem;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Profile Details Grid */
.profile-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.detail-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-icon {
    width: 20px;
    color: #7925c7;
}

.edit-link,
.upgrade-link {
    padding: 0.5rem 1rem;
    border-radius: 10px;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.edit-link {
    background: #f3f4f6;
    color: #374151;
}

.edit-link:hover {
    background: #e5e7eb;
    color: #1f2937;
    text-decoration: none;
}

.upgrade-link {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
}

.upgrade-link:hover {
    background: linear-gradient(135deg, #5b21b6, #7c3aed);
    transform: translateY(-1px);
    text-decoration: none;
    color: white;
}

.card-content {
    padding: 1.5rem;
}

/* Information Grid */
.info-grid {
    display: grid;
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-label i {
    width: 16px;
    color: #9ca3af;
}

.info-value {
    font-size: 1rem;
    color: #1f2937;
    font-weight: 500;
}

.website-link {
    color: #7925c7;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.website-link:hover {
    color: #5b21b6;
    text-decoration: none;
}

/* Subscription Card */
.subscription-status {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 16px;
    border: 2px solid transparent;
}

.status-premium {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    border-color: #10b981;
}

.status-free {
    background: #f8fafc;
    border-color: #e5e7eb;
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.status-premium .status-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.status-free .status-icon {
    background: #64748b;
}

.status-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.status-price {
    font-size: 1rem;
    font-weight: 600;
    color: #7925c7;
    margin-bottom: 0.25rem;
}

.status-description {
    color: #6b7280;
    font-size: 0.9rem;
}

.subscription-actions {
    margin-top: 1rem;
}

.upgrade-btn {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.upgrade-btn:hover {
    background: linear-gradient(135deg, #5b21b6, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(121, 37, 199, 0.3);
    color: white;
    text-decoration: none;
}

/* Security Settings */
.security-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.security-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #64748b, #475569);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.security-content {
    flex: 1;
}

.security-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.security-description {
    font-size: 0.875rem;
    color: #6b7280;
}

.security-status {
    flex-shrink: 0;
}

.status-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.secure {
    background: #dcfce7;
    color: #166534;
}

.status-badge.verified {
    background: #dbeafe;
    color: #1e40af;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .profile-details-grid {
        grid-template-columns: 1fr;
    }
    
    .profile-avatar-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .horizontal-action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 1.875rem;
    }
    
    .profile-header {
        padding: 1.5rem 1rem;
    }
    
    .profile-details-grid {
        gap: 1.5rem;
    }
    
    .card-header {
        padding: 1.25rem;
    }
    
    .card-content {
        padding: 1.25rem;
    }
    
    .horizontal-action-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .horizontal-action-item {
        padding: 0.875rem;
    }
    
    .horizontal-action-icon {
        width: 36px;
        height: 36px;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .profile-avatar {
        width: 100px;
        height: 100px;
    }
    
    .profile-name {
        font-size: 1.5rem;
    }
    
    .card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .card-content {
        padding: 1rem;
    }
    
    .status-indicator {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .security-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .horizontal-action-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 0.5rem;
        padding: 1rem;
    }
    
    .horizontal-action-text {
        text-align: center;
    }
    
    .horizontal-action-title,
    .horizontal-action-description {
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
    }
}

/* Focus states for accessibility */
.edit-link:focus,
.upgrade-link:focus,
.upgrade-btn:focus,
.horizontal-action-item:focus {
    outline: 2px solid #7925c7;
    outline-offset: 2px;
}

/* Enhanced animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.detail-card {
    animation: slideInUp 0.6s ease-out;
}

.detail-card:nth-child(1) { animation-delay: 0.1s; }
.detail-card:nth-child(2) { animation-delay: 0.2s; }
.detail-card:nth-child(3) { animation-delay: 0.3s; }

.horizontal-action-item {
    animation: slideInUp 0.6s ease-out;
}

.horizontal-action-item:nth-child(1) { animation-delay: 0.1s; }
.horizontal-action-item:nth-child(2) { animation-delay: 0.2s; }
.horizontal-action-item:nth-child(3) { animation-delay: 0.3s; }
.horizontal-action-item:nth-child(4) { animation-delay: 0.4s; }

/* Print styles */
@media print {
    .page-header,
    .header-actions,
    .edit-link,
    .upgrade-link,
    .upgrade-btn,
    .profile-quick-actions {
        display: none !important;
    }
    
    .detail-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .profile-overview-card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .card-background {
        background: white !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .detail-card,
    .btn-primary,
    .edit-link,
    .upgrade-link,
    .upgrade-btn,
    .horizontal-action-item {
        transition: none;
        animation: none;
    }
    
    @keyframes slideInUp {
        from, to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}
</style>