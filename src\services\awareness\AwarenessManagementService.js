import ApiService from '@/services/ApiService';
import ApiRoutes from '@/ApiRoutes';

const handleResponse = (res, resolve, reject, errorMessage) => {
    if (parseInt(res.status) === 200) {
        resolve({ data: res.data, msg: res.message });
    } else {
        reject(new Error(res.error || errorMessage));
    }
};

const AwarenessManagementService = {
    
    // Awareness Course routes
    getAllAwareness(keyword = '') {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.GetAwareness, { keyword }, (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch awareness courses")
            );
        });
    },

    getSingleAwareness(courseId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetAwarenessSingle(courseId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch awareness course")
            );
        });
    },

    createAwareness(courseData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.CreateAwareness, courseData, (res) =>
                handleResponse(res, resolve, reject, "Failed to create awareness course")
            );
        });
    },

    updateAwareness(courseId, courseData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.UpdateAwareness(courseId), courseData, (res) =>
                handleResponse(res, resolve, reject, "Failed to update awareness course")
            );
        });
    },

    deleteAwareness(courseId) {
        return new Promise((resolve, reject) => {
            ApiService.DELETE(ApiRoutes.DeleteAwareness(courseId), (res) =>
                handleResponse(res, resolve, reject, "Failed to delete awareness course")
            );
        });
    },

    // Topic routes
    getCourseTopics(courseId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetCourseTopics(courseId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch course topics")
            );
        });
    },

    createCourseTopic(courseId, topicData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.CreateCourseTopics(courseId), topicData, (res) =>
                handleResponse(res, resolve, reject, "Failed to create course topic")
            );
        });
    },

    updateCourseTopic(courseId, topicId, topicData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.UpdateCourseTopics(courseId, topicId), topicData, (res) =>
                handleResponse(res, resolve, reject, "Failed to update course topic")
            );
        });
    },

    deleteCourseTopic(courseId, topicId) {
        return new Promise((resolve, reject) => {
            ApiService.DELETE(ApiRoutes.DeleteCourseTopic(courseId, topicId), (res) =>
                handleResponse(res, resolve, reject, "Failed to delete course topic")
            );
        });
    },

    // Lesson routes
    createLesson(courseId, topicId, lessonData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.CreateLesson(courseId, topicId), lessonData, (res) =>
                handleResponse(res, resolve, reject, "Failed to create lesson")
            );
        });
    },

    updateLesson(courseId, topicId, lessonId, lessonData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.UpdateLesson(courseId, topicId, lessonId), lessonData, (res) =>
                handleResponse(res, resolve, reject, "Failed to update lesson")
            );
        });
    },

    getLesson(courseId, topicId, lessonId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetLesson(courseId, topicId, lessonId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch lesson")
            );
        });
    },

    deleteLesson(courseId, topicId, lessonId) {
        return new Promise((resolve, reject) => {
            ApiService.DELETE(ApiRoutes.DeleteLesson(courseId, topicId, lessonId), (res) =>
                handleResponse(res, resolve, reject, "Failed to delete lesson")
            );
        });
    },
    
    // Question routes
    createOrUpdateQuestion(courseId, topicId, lessonId, questionData) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.CreateOrUpdateAwarenessQuestion(courseId, topicId, lessonId), questionData, (res) =>
                handleResponse(res, resolve, reject, "Failed to create or update question")
            );
        });
    },

    deleteQuestion(courseId, topicId, lessonId, questionId) {
        return new Promise((resolve, reject) => {
            ApiService.DELETE(ApiRoutes.DeleteAwarenessQuestion(courseId, topicId, lessonId, questionId), (res) =>
                handleResponse(res, resolve, reject, "Failed to delete question")
            );
        });
    },

    // File upload routes
    uploadFile(formData) {
        return new Promise((resolve, reject) => {
            ApiService.UPLOAD(ApiRoutes.MEDIA, formData, (res) =>
                handleResponse(res, resolve, reject, "Failed to upload file")
            );
        });
    }
};

export default AwarenessManagementService;