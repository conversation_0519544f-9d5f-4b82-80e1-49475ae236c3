import { ref } from 'vue';
import { useRouter,useRoute } from 'vue-router';
import PricingService from '@/services/PricingService';
import { createToaster } from "@meforma/vue-toaster";
import Swal from 'sweetalert2';


export const usePricingActions = () => {
  const Toaster = createToaster({ position: 'top-right' });
  const adminLoading = ref(false);
  const buttonLoading = ref({});
  const pricing_packages = ref([]);
  const router = useRouter();
  const route = useRoute();
  const subscription_error = ref(null);

  // -------------- Common Functions for Admin and User -------------- //
  // Fetch all pricing packages
  const getAllPricing = async (userType) => {
    if (userType === 'admin') {
      adminLoading.value = true;
    }
    try {
      const { data, msg } = await PricingService.getAllPricing(userType);
      const sortedData = data.sort((a, b) => a.package_level - b.package_level);
      pricing_packages.value = sortedData;
      return { data: sortedData, msg };
    } catch (error) {
      console.error('Failed to fetch pricing packages', error);
      throw error;
    } finally {
      if (userType === 'admin') {
        adminLoading.value = false;
      }
    }
  };

  // -------------- Admin Functions -------------- //
  // Fetch a single pricing package
  const getSinglePricing = async (packageId) => {
    adminLoading.value = true;
    try {
      const { data, msg } = await PricingService.getSinglePricing(packageId);
      return { data, msg };
    } catch (error) {
      console.error('Failed to fetch pricing:', error);
      throw error;
    } finally {
      adminLoading.value = false;
    }
  };

  // Create a new pricing package
  const createPricing = async (pricing) => {
    adminLoading.value = true;
    try {
      const { data, msg } = await PricingService.createPricing(pricing);
      Toaster.success(msg);
      router.push({ name: 'AdminPricingList' });
      return { data, msg };
    } catch (error) {
      console.error('Submission failed:', error);
      throw error;
    } finally {
      adminLoading.value = false;
    }
  };

  // Update an existing pricing package
  const updatePricing = async (packageId, pricing) => {
    adminLoading.value = true;
    try {
      const { data, msg } = await PricingService.updatePricing(packageId, pricing);
      Toaster.success(msg);
      router.push({ name: 'AdminPricingList' });
      return { data, msg };
    } catch (error) {
      console.error('Update failed:', error);
      throw error;
    } finally {
      adminLoading.value = false;
    }
  };

  // Delete a pricing package
  const deletePricing = async (pkg) => {
    const result = await Swal.fire({
      title: 'Delete Package',
      html: "<strong class='text-danger'>Are you sure?</strong> <br><br> <p class='alert alert-danger'>This action will remove all related information of this pricing package permanently.</p>",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ff005a',
      cancelButtonColor: '#777777',
      confirmButtonText: 'Delete',
    });

    if (result.isConfirmed) {
      adminLoading.value = true;
      try {
        const response = await PricingService.deletePricing(pkg._id);
        await getAllPricing('admin');
        Toaster.success(response.msg);
        return response;
      } catch (error) {
        console.error('Failed to delete pricing package', error);
        throw error;
      } finally {
        adminLoading.value = false;
      }
    }
  };

  // Generate buttons for PricingList.vue
  const generateAdminPricingButtons = (pkg) => [
    {
      text: 'Edit',
      loading: adminLoading.value,
      class: 'w-100 btn btn-sm btn-outline-primary rounded-pill me-1',
      action: () => router.push({ name: 'AdminPricingEdit', params: { package_id: pkg._id } }),
    },
    {
      text: 'Delete',
      loading: adminLoading.value,
      class: 'w-100 btn btn-sm btn-outline-danger rounded-pill',
      action: () => deletePricing(pkg),
    },
  ];

  // -------------- User Functions -------------- //
  // Choose a pricing plan
  const choosePlan = async (pkg) => {
    try {
      buttonLoading.value[pkg._id] = true;
      const { data} = await PricingService.choosePlan(pkg);
      window.location.href = data.url;
    } catch (error) {
      console.error('Subscription error:', error);
      throw error;
    } finally {
      buttonLoading.value[pkg._id] = false;
    }
  };

  // Handle subscription success
  const handleSubscriptionSuccess = async () => {
      const sessionId = route.query.session_id;
      if (!sessionId) {
        subscription_error.value = 'No session ID provided.';
        return false;
      }
      try {
        const res = await PricingService.handleSubscriptionSuccess(sessionId);
        localStorage.setItem('UserInfo', JSON.stringify(res.data.user));
        localStorage.setItem('Subscription', JSON.stringify(res.data.package));
        router.push({ name: 'Pricing' });
        return true;
      } catch (err) {
        subscription_error.value = 'Something went wrong while saving your subscription.';
        return false;
      }
  };

  const getSubscription = async () => {
    try {
      const res = await PricingService.getSubscription();
      return res.data;
    } catch (error) {
      console.error('Failed to fetch subscription:', error);
      throw error;
    }
  };

  // Generate buttons for pricing.vue
  const generateUserPricingButtons = (pkg) => [
    {
      text: 'Choose Plan',
      loading: buttonLoading.value[pkg._id] || false,
      class: 'w-100 rounded-pill btn btn-lg btn-outline-success',
      action: () => choosePlan(pkg),
    },
  ];

  return {
    adminLoading,
    buttonLoading,
    pricing_packages,
    getAllPricing,
    getSinglePricing,
    createPricing,
    updatePricing,
    deletePricing,
    choosePlan,
    handleSubscriptionSuccess,
    getSubscription,
    generateAdminPricingButtons,
    generateUserPricingButtons,
  };
};