<template>
    <div class="modern-lesson-container" v-if="lesson != null">
        <LessonHeader :lesson="lesson" :courseId="courseId" />
        <LessonContent v-if="lesson.is_quiz !== '1'" :lesson="lesson" :courseId="courseId" :topicId="topicId" :lessonId="lessonId" @complete="() => markAsComplete(courseId, topicId, lessonId, (data) => emit('refresh', data))" />
        <div v-else class="quiz-wrapper">
            <QuizResults v-if="quizResult != null" :quizResult="quizResult" :lesson="lesson" :courseId="courseId" @retake="retakeQuiz" @downloadCertificate="() => downloadCourseCertificate(courseId)" />
            <QuizTaking v-else :lesson="lesson" :questions="questions" :currentQuestionIndex="currentQuestionIndex" :courseId="courseId" :topicId="topicId" :lessonId="lessonId" @submit="() => submitQuiz(courseId, topicId, lessonId, (data) => emit('refresh', data))" @navigate="handleNavigate" />
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import LessonHeader from './LessonHeader.vue';
import LessonContent from './LessonContent.vue';
import QuizResults from './QuizResults.vue';
import QuizTaking from './QuizTaking.vue';

const route = useRoute();
const emit = defineEmits(['currentLesson', 'refresh']);

const {
  currentLesson: lesson,
  questions,
  quizResult,
  currentQuestionIndex,
  getSingleLesson,
  markAsComplete,
  prevQuestion,
  nextQuestion,
  goToQuestion,
  submitQuiz,
  retakeQuiz,
  downloadCourseCertificate
} = useAwarenessActions();

const courseId = computed(() => route.params.course_id);
const topicId = computed(() => route.params.topic_id);
const lessonId = computed(() => route.params.lesson_id);

const handleNavigate = (action, index = null) => {
  const actions = {
    previous: prevQuestion,
    next: nextQuestion,
    goTo: () => goToQuestion(index)
  };
  actions[action]?.();
};

const loadLesson = async () => {
  await getSingleLesson(courseId.value, topicId.value, lessonId.value);
  emit('currentLesson', { topic_id: topicId.value, lesson_id: lessonId.value });
};

onMounted(async () => {
  await loadLesson();
  window.scrollTo(0, 0);
});

watch(lessonId, async (newLessonId, oldLessonId) => {
  if (newLessonId && newLessonId !== oldLessonId) await loadLesson();
}, { immediate: false });
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/LessonPreview/LessonPreview.scss';
</style>