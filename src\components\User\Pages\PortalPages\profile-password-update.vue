<template>
    <div class="modern-password-update">
        <!-- Page Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">
                        <i class="fa fa-key title-icon"></i>
                        Change Password
                    </h1>
                    <p class="page-subtitle">
                        Update your account password to keep your account secure
                    </p>
                </div>
                <div class="header-actions">
                    <router-link :to="{ name: 'Profile' }" class="btn-secondary">
                        <i class="fa fa-arrow-left"></i>
                        Back to Profile
                    </router-link>
                </div>
            </div>
        </div>

        <!-- Password Update Content -->
        <div class="password-update-content">
            <div class="password-update-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fa fa-shield card-icon"></i>
                        Password Security
                    </div>
                    <div class="security-indicator">
                        <span class="status-badge secure">
                            <i class="fa fa-lock"></i>
                            Secure Connection
                        </span>
                    </div>
                </div>
                
                <div class="card-content">
                    <form id="profilePasswordUpdate" class="password-form" @submit.prevent="updatePassword" enctype="multipart/form-data">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fa fa-lock label-icon"></i>
                                Current Password
                            </label>
                            <div class="input-container">
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    name="current_password" 
                                    placeholder="Enter your current password"
                                    required
                                >
                                <button type="button" class="password-toggle" @click="togglePasswordVisibility('current')">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                            <div class="error-report text-danger"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fa fa-key label-icon"></i>
                                New Password
                            </label>
                            <div class="input-container">
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    name="password" 
                                    placeholder="Enter your new password"
                                    @input="checkPasswordStrength"
                                    required
                                >
                                <button type="button" class="password-toggle" @click="togglePasswordVisibility('new')">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" v-if="passwordStrength.score > 0">
                                <div class="strength-meter">
                                    <div 
                                        class="strength-bar" 
                                        :class="passwordStrength.class"
                                        :style="{ width: passwordStrength.width }"
                                    ></div>
                                </div>
                                <span class="strength-text">{{ passwordStrength.text }}</span>
                            </div>
                            <div class="password-requirements">
                                <div class="requirement-item" :class="{ 'met': requirements.length }">
                                    <i class="fa fa-check requirement-icon"></i>
                                    At least 8 characters
                                </div>
                                <div class="requirement-item" :class="{ 'met': requirements.uppercase }">
                                    <i class="fa fa-check requirement-icon"></i>
                                    One uppercase letter
                                </div>
                                <div class="requirement-item" :class="{ 'met': requirements.lowercase }">
                                    <i class="fa fa-check requirement-icon"></i>
                                    One lowercase letter
                                </div>
                                <div class="requirement-item" :class="{ 'met': requirements.number }">
                                    <i class="fa fa-check requirement-icon"></i>
                                    One number
                                </div>
                            </div>
                            <div class="error-report text-danger"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fa fa-check-circle label-icon"></i>
                                Confirm New Password
                            </label>
                            <div class="input-container">
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    name="password_confirmation" 
                                    placeholder="Re-enter your new password"
                                    @input="checkPasswordMatch"
                                    required
                                >
                                <button type="button" class="password-toggle" @click="togglePasswordVisibility('confirm')">
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-match" v-if="passwordMatch.show">
                                <div class="match-indicator" :class="passwordMatch.class">
                                    <i class="fa" :class="passwordMatch.icon"></i>
                                    {{ passwordMatch.text }}
                                </div>
                            </div>
                            <div class="error-report text-danger"></div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-primary update-btn" :disabled="loading">
                                <i class="fa fa-save" v-if="!loading"></i>
                                <i class="fa fa-spinner fa-spin" v-else></i>
                                {{ loading ? 'Updating...' : 'Update Password' }}
                            </button>
                            <router-link :to="{ name: 'Profile' }" class="btn-cancel">
                                Cancel
                            </router-link>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips Card -->
            <div class="security-tips-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fa fa-lightbulb card-icon"></i>
                        Password Security Tips
                    </div>
                </div>
                <div class="card-content">
                    <div class="tips-grid">
                        <div class="tip-item">
                            <div class="tip-icon">
                                <i class="fa fa-shield"></i>
                            </div>
                            <div class="tip-content">
                                <div class="tip-title">Use Strong Passwords</div>
                                <div class="tip-description">Combine letters, numbers, and symbols</div>
                            </div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">
                                <i class="fa fa-refresh"></i>
                            </div>
                            <div class="tip-content">
                                <div class="tip-title">Update Regularly</div>
                                <div class="tip-description">Change your password every 3-6 months</div>
                            </div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">
                                <i class="fa fa-ban"></i>
                            </div>
                            <div class="tip-content">
                                <div class="tip-title">Avoid Common Passwords</div>
                                <div class="tip-description">Don't use personal information or common words</div>
                            </div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">
                                <i class="fa fa-lock"></i>
                            </div>
                            <div class="tip-content">
                                <div class="tip-title">Keep It Private</div>
                                <div class="tip-description">Never share your password with others</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { createToaster } from "@meforma/vue-toaster";
const Toaster = createToaster({position: 'bottom-right'});

export default {
    name: 'ModernPasswordUpdate',
    data() {
        return {
            loading: false,
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            passwordStrength: {
                score: 0,
                text: '',
                class: '',
                width: '0%'
            },
            requirements: {
                length: false,
                uppercase: false,
                lowercase: false,
                number: false
            },
            passwordMatch: {
                show: false,
                class: '',
                icon: '',
                text: ''
            }
        }
    },
    methods: {
        updatePassword() {
            const THIS = this;
            this.loading = true;
            ApiService.ClearErrorHandler();
            const formData = new FormData(document.getElementById('profilePasswordUpdate'));
            ApiService.POST_FORM_DATA(ApiRoutes.ProfileUpdatePassword, formData, function (res){
                THIS.loading = false;
                if(res.status === 200){
                    Toaster.success(res.msg);
                    THIS.$router.push({name: 'Profile'});
                } else {
                    ApiService.ErrorHandler(res.error);
                }
            })
        },

        togglePasswordVisibility(field) {
            const input = document.querySelector(`input[name="${field === 'current' ? 'current_password' : field === 'new' ? 'password' : 'password_confirmation'}"]`);
            const button = input.nextElementSibling;
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        },

        checkPasswordStrength(event) {
            const password = event.target.value;
            
            // Check requirements
            this.requirements.length = password.length >= 8;
            this.requirements.uppercase = /[A-Z]/.test(password);
            this.requirements.lowercase = /[a-z]/.test(password);
            this.requirements.number = /\d/.test(password);
            
            // Calculate strength score
            let score = 0;
            if (this.requirements.length) score++;
            if (this.requirements.uppercase) score++;
            if (this.requirements.lowercase) score++;
            if (this.requirements.number) score++;
            
            // Special characters bonus
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;
            
            // Update strength display
            this.passwordStrength.score = score;
            
            if (score === 0) {
                this.passwordStrength = { score: 0, text: '', class: '', width: '0%' };
            } else if (score <= 2) {
                this.passwordStrength = { score, text: 'Weak', class: 'weak', width: '25%' };
            } else if (score === 3) {
                this.passwordStrength = { score, text: 'Fair', class: 'fair', width: '50%' };
            } else if (score === 4) {
                this.passwordStrength = { score, text: 'Good', class: 'good', width: '75%' };
            } else {
                this.passwordStrength = { score, text: 'Strong', class: 'strong', width: '100%' };
            }
        },

        checkPasswordMatch(event) {
            const confirmPassword = event.target.value;
            const newPassword = document.querySelector('input[name="password"]').value;
            
            if (confirmPassword.length > 0) {
                this.passwordMatch.show = true;
                if (confirmPassword === newPassword) {
                    this.passwordMatch = {
                        show: true,
                        class: 'match',
                        icon: 'fa-check',
                        text: 'Passwords match'
                    };
                } else {
                    this.passwordMatch = {
                        show: true,
                        class: 'no-match',
                        icon: 'fa-times',
                        text: 'Passwords do not match'
                    };
                }
            } else {
                this.passwordMatch.show = false;
            }
        }
    },
    mounted() {
        window.scrollTo(0, 0);
    }
}
</script>

<style scoped>
.modern-password-update {
    max-width: 800px;
    margin: 0 auto;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

/* Password Update Content */
.password-update-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.password-update-card,
.security-tips-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.password-update-card:hover,
.security-tips-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-icon {
    width: 20px;
    color: #7925c7;
}

.security-indicator {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.status-badge.secure {
    background: #dcfce7;
    color: #166534;
}

.card-content {
    padding: 1.5rem;
}

/* Password Form */
.password-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.label-icon {
    width: 16px;
    color: #7925c7;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    padding-right: 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #7925c7;
    box-shadow: 0 0 0 3px rgba(121, 37, 199, 0.1);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #7925c7;
}

/* Password Strength */
.password-strength {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.strength-meter {
    flex: 1;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    transition: all 0.5s ease;
    border-radius: 3px;
}

.strength-bar.weak {
    background: linear-gradient(90deg, #ef4444, #f87171);
}

.strength-bar.fair {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.strength-bar.good {
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
}

.strength-bar.strong {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.strength-text {
    font-size: 0.875rem;
    font-weight: 600;
    min-width: 50px;
}

.strength-text:has(.weak) { color: #ef4444; }
.strength-text:has(.fair) { color: #f59e0b; }
.strength-text:has(.good) { color: #3b82f6; }
.strength-text:has(.strong) { color: #10b981; }

/* Password Requirements */
.password-requirements {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    transition: all 0.3s ease;
}

.requirement-item.met {
    color: #10b981;
}

.requirement-icon {
    width: 16px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.requirement-item.met .requirement-icon {
    opacity: 1;
}

/* Password Match */
.password-match {
    margin-top: 0.5rem;
}

.match-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
}

.match-indicator.match {
    background: #dcfce7;
    color: #166534;
}

.match-indicator.no-match {
    background: #fee2e2;
    color: #dc2626;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-primary {
    flex: 1;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #5b21b6, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(121, 37, 199, 0.3);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-cancel {
    padding: 1rem 2rem;
    background: #f3f4f6;
    color: #374151;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #e5e7eb;
    color: #1f2937;
    text-decoration: none;
}

/* Security Tips */
.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.tip-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.tip-content {
    flex: 1;
}

.tip-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.tip-description {
    font-size: 0.875rem;
    color: #6b7280;
}

.error-report {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .page-title {
        font-size: 1.875rem;
    }
    
    .card-header {
        padding: 1.25rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .card-content {
        padding: 1.25rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .password-requirements {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-content {
        padding: 1rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.password-update-card,
.security-tips-card {
    animation: slideInUp 0.6s ease-out;
}

.security-tips-card {
    animation-delay: 0.2s;
}

/* Focus states for accessibility */
.btn-primary:focus,
.btn-cancel:focus,
.password-toggle:focus {
    outline: 2px solid #7925c7;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .page-header,
    .header-actions,
    .security-tips-card {
        display: none !important;
    }
    
    .password-update-card {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .password-update-card,
    .security-tips-card,
    .btn-primary,
    .btn-cancel,
    .form-control,
    .strength-bar {
        transition: none;
        animation: none;
    }
}
</style>