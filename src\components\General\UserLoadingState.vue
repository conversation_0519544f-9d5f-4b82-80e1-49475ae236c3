<template>
  <div v-if="loading" class="user-loading-state">
    <div class="loading-spinner" :style="spinnerStyle"> <i :class="spinnerIcon"></i> </div>
    <p class="loading-text">{{ message }}</p>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  loading: { type: Boolean, required: true },
  message: { type: String, default: 'Loading...' },
  spinnerIcon: { type: String, default: 'fa fa-spinner fa-spin' },
  color: { type: String, default: '#059669' }
});

const spinnerStyle = computed(() => ({
  color: props.color
}));
</script>

<style scoped>
@import '@/assets/scss/general/UserLoadingState.scss';
</style>
