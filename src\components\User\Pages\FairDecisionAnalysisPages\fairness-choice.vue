<template>
    <div class="row">
        <div class="col-xl-8 offset-xl-2">
            <div class="w-100">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <a @click="goBack" class="btn btn-sm btn-outline-secondary">
                            <i class="fa fa-fw fa-arrow-left"></i> 
                            <span class="d-sm-inline d-none">Back</span>
                        </a>
                        <h3 class="text-center m-0">
                            <strong>Fair Decision Analysis</strong>
                        </h3>
                        <a class="invisible">
                            <i class="fa fa-fw fa-arrow-left"></i> 
                            <span class="d-sm-inline d-none">Back</span>
                        </a>
                    </div>
                    <div class="card-body py-3 px-sm-5">
                        <div class="w-100 py-5">
                            <div class="row">
                                <!-- General Fairness Analysis -->
                                <div class="col-xl-6 mb-3 mt-3">
                                    <div class="w-100 h-100 text-center py-5 px-3 border shadow d-flex flex-column justify-content-between align-items-center">
                                        <h5 class="mb-4">
                                            If you want to evaluate general fair decision analysis <br> please select
                                        </h5>
                                        <button 
                                            @click="openFdQuestions" 
                                            type="button" 
                                            class="btn btn-outline-success fs-5 m-2 border border-secondary border-2 rounded py-2" 
                                            style="width: 200px; height: 50px;"
                                        >
                                            <strong>General Fairness</strong>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Industry Specific Fairness Analysis -->
                                <div class="col-xl-6 mb-3 mt-3">
                                    <div class="w-100 h-100 text-center py-5 px-3 border shadow d-flex flex-column justify-content-between align-items-center">
                                        <h5 class="mb-4">
                                            If you want to evaluate fairness of specific industry <br> please select
                                        </h5>
                                        <button 
                                            @click="openFdDomain" 
                                            type="button" 
                                            class="btn btn-outline-success fs-5 m-2 border border-secondary border-2 rounded py-2" 
                                            style="width: 200px; height: 50px;"
                                        >
                                            <strong>Industry Specific</strong>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';

export default {
    name: 'FairnessChoice',
    data() {
        return {
            risk_evaluation: null,
            fairnessAnalysisActions: null
        }
    },
    async created() {
        // Set analysis type to fairness
        localStorage.setItem('risk_analysis', 'fair');
        
        // Initialize fairness analysis actions
        this.fairnessAnalysisActions = useFairnessAnalysisActions();
        
        // Load evaluation data
        if (localStorage.getItem('risk_evaluation') == null) {
            this.$router.push({name: 'StartFairDecisionAnalysis'});
        } else {
            this.risk_evaluation = JSON.parse(localStorage.getItem('risk_evaluation'));
        }
    },
    methods: {
        openFdQuestions() {
            // Navigate to general fairness questions
            this.risk_evaluation.category = 'fd';
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
            this.$router.push({name: 'FdQuestions'});
        },
        
        openFdDomain() {
            // Navigate to industry-specific fairness domains
            this.risk_evaluation.category = 'eta-fd';
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
            this.$router.push({name: 'FairnessDomains'});
        },
        
        goBack() {
            this.$router.push({name: 'StartFairDecisionAnalysis'});
        },
        
        async checkFairnessEligibility() {
            try {
                const remaining = await this.fairnessAnalysisActions.countRemainingFairnessAnalysisEvaluations();
                return remaining > 0;
            } catch (error) {
                console.error('Failed to check fairness eligibility:', error);
                return true; // Allow on error
            }
        }
    }
}
</script>

<style scoped>
/* Add any fairness-specific styling here if needed */
.card {
    border-radius: 15px;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.border.shadow {
    border-radius: 10px;
    transition: all 0.3s ease;
}

.border.shadow:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}
</style>