<template>
  <div class="accordion" id="accordionCourse">
    <div v-for="(topic, tIndex) in topics" :key="topic._id" class="each-topics mb-2">
      <div v-if="topic.edit_form !== 1" class="w-100 bg-white shadow-sm p-3 border rounded-3 d-flex justify-content-between align-items-center">
        <h3 class="each-topics-title">
          <a class="text-decoration-none text-dark" href="javascript:void(0)" data-bs-toggle="collapse" :data-bs-target="'#collapse' + tIndex" @click="handleTopicToggle(topic._id)">{{ topic.title }}</a>
        </h3>
        <div class="d-flex gap-3">
          <a @click="topic.edit_form = 1"><i class="fa fa-pencil"></i></a>
          <a @click="handleTopicDelete(topic)" class="text-danger"><i class="fa fa-trash"></i></a>
        </div>
      </div>
      <div v-else class="w-100 bg-white shadow-sm p-2 border rounded-3">
        <input
          type="text"
          class="form-control"
          v-model="topic.title"
          placeholder="Topic Title"
          @blur="handleTopicSave(topic, topic.title)"
          @keyup.enter="handleTopicSave(topic, topic.title)"
          @keyup.escape="topic.edit_form = 0"
        >
      </div>
      <div class="each-topic-lesson collapse" :class="{ 'show': activeTopic === topic._id }" :id="'collapse' + tIndex" data-bs-parent="#accordionCourse">
        <LessonList
          :lessons="topic.lessons || []"
          :course-id="courseId"
          :topic-id="topic._id"
          :active-lesson="activeLesson"
          @lesson-selected="$emit('lesson-selected', $event)"
          @lesson-deleted="$emit('lesson-deleted', $event)"
        />
      </div>
    </div>

    <div class="each-topics" v-if="!showCreateForm">
      <a class="btn btn-secondary w-100 btn-lg" @click="showCreateForm = true"> <i class="fa fa-plus"></i> New Topic </a>
    </div>
    
    <div class="each-topics" v-if="showCreateForm">
      <div class="w-100 bg-white shadow-sm p-2 border rounded-3">
        <input 
          ref="createInput"
          type="text" 
          class="form-control" 
          v-model="newTopicTitle" 
          placeholder="Topic Title" 
          @blur="handleCreateTopic"
          @keyup.enter="handleCreateTopic"
          @keyup.escape="cancelCreate"
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';
import LessonList from './lessons/LessonList.vue';

const props = defineProps({
  topics: { type: Array, default: () => [] },
  courseId: { type: String, required: true },
  activeTopic: { type: String, default: null },
  activeLesson: { type: String, default: null }
});

const emit = defineEmits([
  'topic-created', 
  'topic-updated', 
  'topic-deleted', 
  'topic-selected',
  'lesson-selected',
  'lesson-deleted'
]);

const createInput = ref(null);
const showCreateForm = ref(false);
const newTopicTitle = ref('');

const handleTopicToggle = (topicId) => {
  emit('topic-selected', topicId);
};

const handleTopicSave = (topic, newTitle) => {
  const trimmedTitle = newTitle?.trim();
  if (trimmedTitle) {
    emit('topic-updated', { topicId: topic._id, title: trimmedTitle });
    topic.edit_form = 0;
  }
};

const handleTopicDelete = (topic) => {
  emit('topic-deleted', topic);
};

const handleCreateTopic = () => {
  const trimmedTitle = newTopicTitle.value.trim();
  if (trimmedTitle) {
    emit('topic-created', { title: trimmedTitle });
    cancelCreate();
  }
};

const cancelCreate = () => {
  newTopicTitle.value = '';
  showCreateForm.value = false;
};

watch(showCreateForm, async (show) => {
  if (show) {
    await nextTick();
    if (createInput.value) createInput.value.focus();
  }
});
</script>
