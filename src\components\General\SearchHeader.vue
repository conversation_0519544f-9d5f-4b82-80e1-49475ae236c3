<template>
  <div class="w-100 bg-white p-3 shadow mb-4 d-flex justify-content-between align-items-center">
    <input 
      type="text" 
      class="form-control" 
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      @keyup="$emit('search')"
      :style="{ width: searchWidth }" 
      :placeholder="placeholder"
    >
    <router-link class="btn btn-primary rounded-pill px-3 btn-sm" :to="createRoute">
      {{ createButtonText }}
    </router-link>
  </div>
</template>

<script setup>
defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Search...'
  },
  searchWidth: {
    type: String,
    default: '300px'
  },
  createRoute: {
    type: Object,
    required: true
  },
  createButtonText: {
    type: String,
    default: 'Create New'
  }
});

defineEmits(['update:modelValue', 'search']);
</script>
