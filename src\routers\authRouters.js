import Register from "@/components/User/Pages/AuthPages/register.vue";
import Login from "@/components/User/Pages/AuthPages/login.vue";
import ForgotPassword from "@/components/User/Pages/AuthPages/forgot_password.vue";
import ResetPassword from "@/components/User/Pages/AuthPages/reset_password.vue";

const USER_AUTH_ROOT_URL = "/user/auth/";

export default [
    { path: USER_AUTH_ROOT_URL + 'register', name: 'Register', component: Register },
    { path: USER_AUTH_ROOT_URL + 'login', name: 'Login', component: Login },
    { path: USER_AUTH_ROOT_URL + 'forgot-password', name: 'ForgotPassword', component: ForgotPassword },
    { path: USER_AUTH_ROOT_URL + 'reset-password/:resetCode', name: 'ResetPassword', component: ResetPassword, props: true },
]