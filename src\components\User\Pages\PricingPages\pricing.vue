<template>
  <div class="minimal-pricing-page">
    <!-- Header Section -->
    <div class="pricing-header">
      <div class="header-content">
        <button 
          @click="router.push({ name: 'Dashboard' })" 
          class="close-button"
          aria-label="Close Pricing"
        >
          <i class="fa fa-times"></i>
        </button>
        
        <div class="header-text">
          <h1 class="header-title">Choose Your Plan</h1>
          <p class="header-subtitle">
            Simple, transparent pricing for AI risk evaluation and fairness analysis
          </p>
        </div>
      </div>
    </div>

    <!-- Pricing Cards Section -->
    <div class="pricing-section">
      <div class="pricing-container">
        <div class="pricing-grid" :style="{ '--total-plans': pricing_packages.length }">
          <div 
            v-for="(pkg, index) in pricing_packages" 
            :key="pkg._id"
            class="pricing-card"
            :class="[
              getPlanTypeClass(pkg.package_name, index),
              { 
                'featured': isFeaturedPlan(pkg, index),
                'current': isCurrentPlan(pkg)
              }
            ]"
          >
            <!-- Popular Badge -->
            <div v-if="isPopularPlan(pkg, index)" class="popular-badge">
              Most Popular
            </div>

            <!-- Current Plan Badge -->
            <div v-if="isCurrentPlan(pkg)" class="current-badge">
              <i class="fa fa-check"></i>
              Current Plan
            </div>

            <!-- Card Content -->
            <div class="card-content">
              <!-- Plan Header -->
              <div class="plan-header">
                <div class="plan-icon">
                  <i :class="getPlanIcon(pkg.package_name, index)"></i>
                </div>
                <h3 class="plan-name">{{ pkg.package_name }}</h3>
                <div class="plan-price">
                  <span v-if="pkg.package_price === 0" class="price-free">Free</span>
                  <template v-else>
                    <span class="price-amount">
                      <span class="currency">{{ getCurrencySymbol(pkg.currency) }}</span>{{ pkg.package_price }}
                    </span>
                  </template>
                </div>
              </div>

              <!-- Features List -->
              <div class="features-section">
                <div class="features-list">
                  <div 
                    v-for="(feature, fIndex) in getEssentialFeatures(pkg)" 
                    :key="fIndex"
                    class="feature-item"
                    :class="{ 'disabled': !feature.included }"
                  >
                    <div class="feature-check">
                      <i :class="feature.included ? 'fa fa-check' : 'fa fa-minus'"></i>
                    </div>
                    <span class="feature-text">{{ feature.text }}</span>
                  </div>
                </div>
              </div>

              <!-- Action Button -->
              <div class="card-action">
                <button
                  v-if="!isCurrentPlan(pkg)"
                  @click="choosePlan(pkg)"
                  :disabled="buttonLoading[pkg._id]"
                  class="plan-button"
                  :class="{ 'loading': buttonLoading[pkg._id] }"
                >
                  <span v-if="!buttonLoading[pkg._id]">
                    {{ pkg.package_price === 0 ? 'Get Started' : 'Choose Plan' }}
                  </span>
                  <span v-else class="loading-content">
                    <i class="fa fa-spinner fa-spin"></i>
                    Processing...
                  </span>
                </button>
                
                <div v-else class="current-status">
                  <i class="fa fa-check-circle"></i>
                  Active Plan
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Comparison (Minimal) -->
    <div class="comparison-section" v-if="pricing_packages.length > 1">
      <div class="comparison-container">
        <h2 class="comparison-title">Feature Comparison</h2>
        <div class="comparison-grid">
          <div class="comparison-header">
            <div class="feature-column-header">Features</div>
            <div 
              v-for="pkg in pricing_packages" 
              :key="pkg._id"
              class="plan-column-header"
              :class="getPlanTypeClass(pkg.package_name, pricing_packages.indexOf(pkg))"
            >
              {{ pkg.package_name }}
            </div>
          </div>
          
          <div 
            v-for="(comparison, index) in getComparisonFeatures()" 
            :key="index"
            class="comparison-row"
          >
            <div class="feature-name">{{ comparison.feature }}</div>
            <div 
              v-for="pkg in pricing_packages" 
              :key="pkg._id"
              class="feature-value"
            >
              <span v-if="comparison.getValue">
                {{ comparison.getValue(pkg) }}
              </span>
              <i 
                v-else
                :class="comparison.hasFeature(pkg) ? 'fa fa-check text-success' : 'fa fa-times text-muted'"
              ></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { usePricingActions } from '@/composables/pricing/usePricingActions';
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';
import { useRiskEvaluationActions } from '@/composables/riskEvaluation/useRiskEvaluationActions';

const router = useRouter();
const { pricing_packages, getAllPricing, choosePlan, buttonLoading } = usePricingActions();

const currentPackage = JSON.parse(localStorage.getItem("Subscription"));

// Currency symbol helper
const getCurrencySymbol = (currency) => {
  const symbols = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'CHF',
    'CNY': '¥',
    'INR': '₹'
  };
  return symbols[currency?.toUpperCase()] || currency || '$';
};

// Dynamic plan classification
const getPlanTypeClass = (planName, index) => {
  const name = planName.toLowerCase();
  const totalPlans = pricing_packages.value.length;
  
  // If specific tier names are detected
  if (name.includes('bronze') || name.includes('basic')) return 'plan-bronze';
  if (name.includes('silver') || name.includes('standard')) return 'plan-silver';
  if (name.includes('gold') || name.includes('premium')) return 'plan-gold';
  if (name.includes('platinum') || name.includes('enterprise')) return 'plan-platinum';
  
  // Dynamic assignment based on position and total plans
  if (totalPlans === 2) {
    return index === 0 ? 'plan-bronze' : 'plan-gold';
  } else if (totalPlans === 3) {
    return ['plan-bronze', 'plan-silver', 'plan-gold'][index] || 'plan-bronze';
  } else if (totalPlans === 4) {
    return ['plan-bronze', 'plan-silver', 'plan-gold', 'plan-platinum'][index] || 'plan-bronze';
  }
  
  // Fallback for more than 4 plans
  const planTypes = ['plan-bronze', 'plan-silver', 'plan-gold', 'plan-platinum'];
  return planTypes[index % 4] || 'plan-bronze';
};

const getPlanIcon = (planName, index) => {
  const name = planName.toLowerCase();
  
  if (name.includes('bronze') || name.includes('basic')) return 'fa fa-user';
  if (name.includes('silver') || name.includes('standard')) return 'fa fa-users';
  if (name.includes('gold') || name.includes('premium')) return 'fa fa-star';
  if (name.includes('platinum') || name.includes('enterprise')) return 'fa fa-crown';
  
  // Dynamic icons based on position
  const icons = ['fa fa-user', 'fa fa-users', 'fa fa-star', 'fa fa-crown'];
  return icons[index] || 'fa fa-circle';
};

const isCurrentPlan = (pkg) => {
  return currentPackage && currentPackage._id === pkg._id;
};

const isFeaturedPlan = (pkg, index) => {
  const totalPlans = pricing_packages.value.length;
  const name = pkg.package_name.toLowerCase();
  
  // Feature the highest paid plan or gold/premium
  if (name.includes('gold') || name.includes('premium') || name.includes('platinum')) return true;
  
  // For 3 plans, feature the middle one (silver/standard)
  if (totalPlans === 3 && index === 1) return true;
  
  return false;
};

const isPopularPlan = (pkg, index) => {
  const totalPlans = pricing_packages.value.length;
  const name = pkg.package_name.toLowerCase();
  
  // Popular plans logic
  if (name.includes('gold') || name.includes('premium')) return true;
  if (totalPlans === 3 && index === 1) return true;
  if (totalPlans === 4 && index === 2) return true;
  
  return false;
};

const getEssentialFeatures = (pkg) => {
  const features = [];

  // Risk evaluation
  if (pkg.risk_evaluation?.status) {
    const sessions = pkg.risk_evaluation.sessions || 'Unlimited';
    features.push({ 
      text: `Risk Evaluation (${sessions} sessions)`, 
      included: true 
    });
  } else {
    features.push({ text: 'Risk Evaluation', included: false });
  }

  // Fair decision analysis
  if (pkg.fair_decision_analysis?.status) {
    const sessions = pkg.fair_decision_analysis.sessions || 'Unlimited';
    features.push({ 
      text: `Fairness Analysis (${sessions} sessions)`, 
      included: true 
    });
  } else {
    features.push({ text: 'Fairness Analysis', included: false });
  }

  // Training
  features.push({ text: 'Training Courses', included: !!pkg.training });

  // Advanced features for paid plans
  if (pkg.package_price > 0) {
    features.push({ text: 'Advanced Analytics', included: true });
    features.push({ text: 'Priority Support', included: true });
    
    if (pkg.online_consultancy_hours) {
      features.push({ 
        text: `Online Consultancy (${pkg.online_consultancy_hours}h)`, 
        included: true 
      });
    }
    
    if (pkg.in_person_services?.status) {
      features.push({ text: 'In-Person Services', included: true });
    }
  } else {
    features.push({ text: 'Email Support', included: true });
  }

  return features.slice(0, 6); // Limit to 6 key features for minimal design
};

const getComparisonFeatures = () => {
  return [
    {
      feature: 'Risk Evaluation Sessions',
      getValue: (pkg) => {
        if (!pkg.risk_evaluation?.status) return '0';
        return pkg.risk_evaluation.sessions || 'Unlimited';
      }
    },
    {
      feature: 'Fairness Analysis Sessions',
      getValue: (pkg) => {
        if (!pkg.fair_decision_analysis?.status) return '0';
        return pkg.fair_decision_analysis.sessions || 'Unlimited';
      }
    },
    {
      feature: 'Training Courses',
      hasFeature: (pkg) => !!pkg.training
    },
    {
      feature: 'Online Consultancy',
      getValue: (pkg) => pkg.online_consultancy_hours ? `${pkg.online_consultancy_hours} hours` : 'Not included'
    },
    {
      feature: 'Priority Support',
      hasFeature: (pkg) => pkg.package_price > 0
    }
  ];
};

onMounted(async () => {
  await getAllPricing('user');
});
</script>

<style scoped>
.minimal-pricing-page {
  min-height: 100vh;
  background: #fafbfc;
}

/* Header Section */
.pricing-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 3rem 2rem 2rem;
  position: relative;
  border-bottom: 1px solid #e2e8f0;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
}

.close-button {
  position: absolute;
  top: -1rem;
  right: 0;
  width: 40px;
  height: 40px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #e2e8f0;
  color: #374151;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 0.75rem;
  letter-spacing: -0.025em;
}

.header-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* Pricing Section */
.pricing-section {
  padding: 4rem 2rem;
}

.pricing-container {
  max-width: 1200px;
  margin: 0 auto;
}

.pricing-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(var(--total-plans, 3), 1fr);
  max-width: 1000px;
  margin: 0 auto;
}

/* Pricing Cards */
.pricing-card {
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
  border-color: #cbd5e1;
}

.pricing-card.featured {
  border-width: 2px;
  border-color: #3b82f6;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
}

.pricing-card.featured:hover {
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
}

/* Plan Type Colors - Minimal Metallic */
.plan-bronze {
  border-left: 4px solid #92400e;
}

.plan-bronze .plan-button {
  background: #92400e;
}

.plan-bronze .plan-button:hover:not(:disabled) {
  background: #78350f;
}

.plan-silver {
  border-left: 4px solid #6b7280;
}

.plan-silver .plan-button {
  background: #6b7280;
}

.plan-silver .plan-button:hover:not(:disabled) {
  background: #4b5563;
}

.plan-gold {
  border-left: 4px solid #d97706;
}

.plan-gold .plan-button {
  background: #d97706;
}

.plan-gold .plan-button:hover:not(:disabled) {
  background: #b45309;
}

.plan-platinum {
  border-left: 4px solid #5b21b6;
}

.plan-platinum .plan-button {
  background: #5b21b6;
}

.plan-platinum .plan-button:hover:not(:disabled) {
  background: #4c1d95;
}

/* Badges */
.popular-badge,
.current-badge {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.375rem 1rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 2;
  white-space: nowrap;
}

.popular-badge {
  background: #3b82f6;
  color: white;
}

.current-badge {
  background: #10b981;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Card Content */
.card-content {
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.plan-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 1rem;
  background: #f8fafc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: #64748b;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 0.75rem;
}

.plan-price {
  margin-bottom: 0.5rem;
}

.price-free {
  font-size: 2rem;
  font-weight: 700;
  color: #059669;
}

.price-amount {
  font-size: 2.5rem;
  font-weight: 800;
  color: #0f172a;
  line-height: 1;
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  margin-right: 0.125rem;
}

/* Features Section */
.features-section {
  flex: 1;
  margin-bottom: 2rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
}

.feature-item.disabled {
  opacity: 0.5;
}

.feature-check {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  flex-shrink: 0;
}

.feature-item:not(.disabled) .feature-check {
  background: #dcfce7;
  color: #166534;
}

.feature-item.disabled .feature-check {
  background: #f1f5f9;
  color: #9ca3af;
}

.feature-text {
  font-weight: 500;
}

/* Action Button */
.card-action {
  margin-top: auto;
}

.plan-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: #0f172a;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.plan-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.plan-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.current-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem;
  background: #dcfce7;
  color: #166534;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Comparison Section */
.comparison-section {
  padding: 3rem 2rem;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
}

.comparison-container {
  max-width: 1000px;
  margin: 0 auto;
}

.comparison-title {
  font-size: 1.75rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  color: #0f172a;
}

.comparison-grid {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.comparison-header {
  display: grid;
  grid-template-columns: 2fr repeat(var(--total-plans, 3), 1fr);
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.feature-column-header,
.plan-column-header {
  padding: 1rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.feature-column-header {
  text-align: left;
}

.plan-column-header {
  text-align: center;
}

.comparison-row {
  display: grid;
  grid-template-columns: 2fr repeat(var(--total-plans, 3), 1fr);
  border-bottom: 1px solid #f1f5f9;
}

.comparison-row:last-child {
  border-bottom: none;
}

.feature-name,
.feature-value {
  padding: 1rem;
  font-size: 0.875rem;
}

.feature-name {
  font-weight: 500;
  color: #374151;
  text-align: left;
}

.feature-value {
  text-align: center;
  color: #64748b;
}

.text-success {
  color: #059669;
}

.text-muted {
  color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .pricing-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    max-width: none;
  }
  
  .comparison-header,
  .comparison-row {
    grid-template-columns: 1.5fr repeat(var(--total-plans, 3), 1fr);
  }
  
  .feature-column-header,
  .plan-column-header,
  .feature-name,
  .feature-value {
    padding: 0.75rem 0.5rem;
    font-size: 0.8125rem;
  }
}

@media (max-width: 768px) {
  .pricing-header {
    padding: 2rem 1rem 1.5rem;
  }
  
  .header-title {
    font-size: 2rem;
  }
  
  .pricing-section {
    padding: 3rem 1rem;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .comparison-section {
    padding: 2rem 1rem;
  }
  
  .comparison-grid {
    overflow-x: auto;
  }
  
  .comparison-header,
  .comparison-row {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 1.75rem;
  }
  
  .header-subtitle {
    font-size: 1rem;
  }
  
  .card-content {
    padding: 1.5rem 1.25rem;
  }
  
  .plan-name {
    font-size: 1.25rem;
  }
  
  .price-amount {
    font-size: 2rem;
  }
  
  .popular-badge,
  .current-badge {
    font-size: 0.6875rem;
    padding: 0.25rem 0.75rem;
  }
}

/* Focus States */
.plan-button:focus,
.close-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .pricing-card,
  .plan-button,
  .close-button {
    transition: none;
  }
  
  .fa-spin {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .close-button,
  .plan-button,
  .current-status {
    display: none !important;
  }
  
  .pricing-card {
    break-inside: avoid;
    border: 1px solid #000;
    box-shadow: none;
  }
}
</style>