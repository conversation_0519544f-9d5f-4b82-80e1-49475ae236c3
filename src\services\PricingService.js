import ApiRoutes from "@/ApiRoutes";
import ApiService from "@/services/ApiService";

const handleResponse = (response, resolve, reject, errorMsg) => {
    if (response?.status === 200) {
        resolve(response);
    } else {
        reject(new Error(errorMsg));
    }
};

const PricingService = {
    getAllPricing(userType) {
        if (userType === "admin"){
            return new Promise((resolve, reject) => {
                ApiService.GET(ApiRoutes.AdminPricingList, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch all pricing")
                );
            });
        }
        else {
            return new Promise((resolve, reject) => {
                ApiService.GET(ApiRoutes.PricingList, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch all pricing")
                );
            });
        }
    },

    createPricing(pricing) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.PricingCreate, pricing, (res) =>
                handleResponse(res, resolve, reject, "Failed to create pricing")
            );
        });
    },

    deletePricing(packageId) {
        return new Promise((resolve, reject) => {
            ApiService.DELETE(ApiRoutes.PricingDelete(packageId), (res) =>
                handleResponse(res, resolve, reject, "Failed to delete pricing")
            );
        });
    },

    getSinglePricing(packageId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.PricingSingle(packageId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch pricing")
            );
        });
    },

    updatePricing(packageId, pricing) {
        return new Promise((resolve, reject) => {
            ApiService.PUT(ApiRoutes.PricingUpdate(packageId), pricing, (res) =>
                handleResponse(res, resolve, reject, "Failed to update pricing")
            );
        });
    },

    choosePlan(pkg) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.SubscribeCheckout, {pkg} ,(res) =>
                handleResponse(res, resolve, reject, "Failed to purchase plan")
            );
        });
    },

    handleSubscriptionSuccess(sessionId) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.SubscribeSuccess, { session_id: sessionId }, (res) =>
                handleResponse(res, resolve, reject, "Failed to save subscription")
            );
        });
    },

    getSubscription() {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.Subscription, (res) =>
                handleResponse(res, resolve, reject, "Failed to save subscription")
            );
        });
    },
};

export default PricingService;