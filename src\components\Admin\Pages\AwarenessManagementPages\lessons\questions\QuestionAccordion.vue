<template>
  <div class="accordion" id="lesson_quiz">
    <div class="accordion-item" v-for="(question, qIndex) in questions" :key="qIndex" :id="'eachQuestion_' + qIndex">
      <h2 class="accordion-header bg-white" :id="'EvSystemQuestion_' + qIndex">
        <button
          class="accordion-button bg-white"
          :class="{ 'collapsed': !isNewQuestion(question) }"
          type="button"
          data-bs-toggle="collapse"
          :data-bs-target="'#EvSystemQuestionCollapse_' + qIndex"
          :aria-expanded="isNewQuestion(question)"
          aria-controls="collapseOne"
        >
          <strong v-if="question.question === ''">  Question #{{ qIndex + 1 }}</strong>
          <strong v-else> #{{ qIndex + 1 }} - {{ question.question }} </strong>
        </button>
      </h2>
      
      <div :id="'EvSystemQuestionCollapse_' + qIndex" class="accordion-collapse collapse" :class="{ 'show': isNewQuestion(question) }" data-bs-parent="#lesson_quiz">
        <div class="accordion-body">
          <QuestionForm :question="question" :question-index="qIndex" :loading="loading" @save="handleQuestionSave" @delete="handleQuestionDelete"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import QuestionForm from './QuestionForm.vue';

const props = defineProps({
  questions: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false }
});

const emit = defineEmits(['question-saved', 'question-deleted']);

const isNewQuestion = (question) => {
  return question._id === null && question.question === '';
};

const handleQuestionSave = (payload) => {
  emit('question-saved', payload);
};

const handleQuestionDelete = (payload) => {
  emit('question-deleted', payload);
};
</script>
