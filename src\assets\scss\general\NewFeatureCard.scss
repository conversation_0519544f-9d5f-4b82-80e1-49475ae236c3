.new-feature-card {
  background: white;
  border-radius: 20px;
  border: 2px dashed #d1d5db;
  background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out;
}

.new-feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.card-link {
  display: flex;
  flex-direction: column;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  height: 100%;
  position: relative;
  z-index: 2;
}

.card-content {
  flex: 1;
  padding: 0;
  position: relative;
  z-index: 2;
}

.new-feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.card-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
}

.action-text {
  flex: 1;
  text-align: left;
  font-weight: 600;
  color: #374151;
}

.card-footer i {
  color: #6b7280;
  transition: transform 0.3s ease;
}

.new-feature-card:hover .card-footer i {
  transform: translateX(4px);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .card-link {
    padding: 1.25rem;
  }
  
  .new-feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .card-title {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .card-link {
    padding: 1rem;
  }
  
  .new-feature-icon {
    width: 45px;
    height: 45px;
    font-size: 1.125rem;
  }
}

/* Focus states for accessibility */
.card-link:focus {
  outline: 2px solid #059669;
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .new-feature-card {
    animation: none;
  }
  
  .card-link,
  .card-footer i {
    transition: none;
  }
}
