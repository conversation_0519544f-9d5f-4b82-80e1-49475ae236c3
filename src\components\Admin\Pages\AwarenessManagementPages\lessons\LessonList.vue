<template>
  <div class="w-100">
    <ol class="m-0 p-0">
      <li v-for="(lesson, lIndex) in lessons" :key="lIndex" :class="{ 'active_lesson': activeLesson === lesson._id }" class="py-2 px-3 bg-light border mb-2 lesson_hover d-flex justify-content-between align-items-center">
        <router-link class="text-decoration-none" :to="{ name: 'AdminLessonPreview', params: { course_id: courseId, topic_id: topicId, lesson_id: lesson._id } }" @click="handleLessonSelected(lesson)"> <strong class="text-secondary">{{ lesson.title }}</strong> </router-link>
        <div class="d-flex gap-3">
          <router-link :to="{ name: 'LessonEdit', params: { course_id: courseId, topic_id: topicId, lesson_id: lesson._id } }" class="text-decoration-none"> <i class="fa fa-pencil"></i> </router-link>
          <a @click="handleLessonDelete(lesson)" class="text-decoration-none text-danger"> <i class="fa fa-trash"></i> </a>
        </div>
      </li>
    </ol>
    <div class="each-lesson text-center mt-2">
      <router-link :to="{ name: 'LessonCreate', params: { course_id: courseId, topic_id: topicId } }" class="btn btn-sm btn-secondary"><i class="fa fa-plus"></i> New Lesson</router-link>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  lessons: { type: Array, default: () => [] },
  courseId: { type: String, required: true },
  topicId: { type: String, required: true },
  activeLesson: { type: String, default: null }
});

const emit = defineEmits(['lesson-selected', 'lesson-deleted']);

const handleLessonSelected = (lesson) => {
  emit('lesson-selected', { lesson_id: lesson._id, topic_id: props.topicId });
};

const handleLessonDelete = (lesson) => {
  emit('lesson-deleted', { topicId: props.topicId, lesson });
};
</script>
