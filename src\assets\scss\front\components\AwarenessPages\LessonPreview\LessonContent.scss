/* Regular Lesson Content */
.lesson-content-wrapper {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.lesson-content-card {
    padding: 2.5rem;
}

.lesson-title-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.lesson-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.lesson-meta {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.completed-icon {
    color: #059669;
}

.lesson-content {
    line-height: 1.7;
}

.content-body {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 2rem;
}

.content-body h1, .content-body h2, .content-body h3, .content-body h4, .content-body h5, .content-body h6 {
    color: #1f2937;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.content-body p {
    margin-bottom: 1rem;
}

.content-body ul, .content-body ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.content-body li {
    margin-bottom: 0.5rem;
}

.content-body blockquote {
    border-left: 4px solid #059669;
    background: #f0fdf4;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 8px 8px 0;
}

.content-body code {
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875em;
}

.content-body pre {
    background: #1f2937;
    color: #e5e7eb;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
}

.lesson-actions {
    display: flex;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.complete-lesson-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.complete-lesson-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.completion-message {
    display: flex;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.completion-card {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-radius: 16px;
    border: 1px solid #a7f3d0;
}

.completion-icon {
    font-size: 3rem;
    color: #059669;
    margin-bottom: 1rem;
}

.completion-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.completion-card p {
    color: #047857;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lesson-content-card {
        padding: 1.5rem;
    }

    .lesson-title {
        font-size: 1.5rem;
    }
}

/* Enhanced Animations */
.lesson-content-wrapper {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.completion-card {
    animation: bounceIn 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Focus States for Accessibility */
.complete-lesson-btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .lesson-content-wrapper {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .lesson-content-wrapper,
    .completion-card {
        animation: none;
    }
}
