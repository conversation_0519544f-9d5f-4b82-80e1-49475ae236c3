const BASE_URL = import.meta.env.VITE_API_URL;
const ApiUrl = `${BASE_URL}/api/portal`;

const FairnessApiRoutes = {
    // Fairness Analysis specific routes
    FairnessEvaluationSubmit: ApiUrl + "/fairness/evaluation/submit",
    FairnessAnalysisQuestions: ApiUrl + "/fairness/evaluation/questions",
    FairnessSectorDetails: ApiUrl + "/fairness/evaluation/sector/details",
    FairnessSectors: ApiUrl + "/fairness/evaluation/sectors",
    FairnessEvaluationDetails: ApiUrl + "/fairness/evaluation/details",
    FairnessEvaluationReport: ApiUrl + "/fairness/evaluation/report",
    FairnessEvaluationCertificate: ApiUrl + "/fairness/evaluation/certificate",
    
    // These routes are currently shared but could be made fairness-specific in the future
    // For now, they remain pointing to existing endpoints to maintain compatibility
    FdSectorDetails: ApiUrl + "/risk/evaluation/fd/sector/details",
    EvaluationFdQuestions: ApiUrl + "/risk/evaluation/fd/questions", 
    FdSectors: ApiUrl + "/risk/evaluation/fd/sectors",
    
    // User fairness evaluations (filtered from main evaluations endpoint)
    UserFairnessEvaluations: ApiUrl + "/user/fairness/evaluation/all",
    UserFairnessEvaluationDelete: ApiUrl + "/user/fairness/evaluation/delete",
    UserFairnessEvaluationReport: ApiUrl + "/user/fairness/evaluation/report",
    UserFairnessEvaluationDetails: ApiUrl + "/user/fairness/evaluation/details",
    UserFairnessEvaluationQuestionSingle: ApiUrl + "/user/fairness/evaluation/question/single",
    UserFairnessEvaluationQuestionSingleChatGPT: ApiUrl + "/user/fairness/evaluation/question/single/chatGPT",
    UserFairnessEvaluationQuestionSingleUpdate: ApiUrl + "/user/fairness/evaluation/question/single/update",
};

export default FairnessApiRoutes;