import authApiRoutes from '@/ApiRoutes/authApiRoutes';
import portalApiRoutes from '@/ApiRoutes/portalApiRoutes';
import awarenessApiRoutes from '@/ApiRoutes/awarenessApiRoutes';
import adminApiRoutes from '@/ApiRoutes/adminApiRoutes';
import FairnessApiRoutes from './fairnessApiRoutes';

const BASE_URL = import.meta.env.VITE_API_URL;
const ApiUrl = `${BASE_URL}/api`;

const ApiRoutes = {
  // Spread all route modules - RESTORE ORIGINAL STRUCTURE
  ...authApiRoutes,
  ...portalApiRoutes,
  ...awarenessApiRoutes,
  ...adminApiRoutes,
  
  // General API routes - KEEP ALL ORIGINAL
  PricingList: `${ApiUrl}/pricing/list`,
  SubscribeCheckout: `${ApiUrl}/subscribe/checkout`,
  SubscribeSuccess: `${ApiUrl}/subscribe/success`,
  SubscribeCancel: `${ApiUrl}/subscribe/cancel`,
  Subscription: `${ApiUrl}/subscription`,
  
  // Search routes
  Search: `${ApiUrl}/search`,
  SearchSuggestions: `${ApiUrl}/search/suggestions`,
  
  // Notification routes
  Notifications: `${ApiUrl}/notifications`,
  NotificationsCount: `${ApiUrl}/notifications/count`,
  NotificationsMarkRead: `${ApiUrl}/notifications/mark-read`,
  NotificationsMarkAllRead: `${ApiUrl}/notifications/mark-all-read`,
  NotificationDelete: (id) => `${ApiUrl}/notifications/${id}`,
  

  // Fairness Analysis Routes
  FairnessAnalysisQuestions: FairnessApiRoutes.FairnessAnalysisQuestions,
  FairnessSectorDetails: FairnessApiRoutes.FairnessSectorDetails,
  FairnessSectors: FairnessApiRoutes.FairnessSectors,
  
  // Keep existing FD routes for backward compatibility but ensure they work with fairness
  EvaluationFdQuestions: FairnessApiRoutes.EvaluationFdQuestions,
  FdSectorDetails: FairnessApiRoutes.FdSectorDetails,
  FdSectors: FairnessApiRoutes.FdSectors,

};

export default ApiRoutes;