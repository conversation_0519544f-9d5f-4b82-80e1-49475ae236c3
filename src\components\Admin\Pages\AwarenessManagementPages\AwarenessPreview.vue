<template>
    <div class="container-fluid">
        <LoadingSpinner :loading="loading" />

        <div class="w-100">
            <div class="card" v-if="course">
                <div class="card-header bg-white py-3">
                    <h4 class="m-0 fw-bold text-secondary">{{ course.title }}</h4>
                </div>

                <div class="card-body p-0">
                    <div class="course-preview-board">
                        <div class="course-preview-board-sidebar">
                            <SidebarManagement
                                :topics="topics"
                                :course-id="courseId"
                                :active-topic="currentAwarenessPage.activeTopicId"
                                :active-lesson="currentAwarenessPage.activeLessonId"
                                @topic-created="handlers.topicCreated"
                                @topic-updated="handlers.topicUpdated"
                                @topic-deleted="handlers.topicDeleted"
                                @topic-selected="currentAwarenessPage.setActiveTopic"
                                @lesson-selected="currentAwarenessPage.setActiveLesson"
                                @lesson-deleted="handlers.lessonDeleted"
                            />
                        </div>

                        <div class="course-preview-board-content bg-light">
                            <CourseOverview v-if="showCourseOverview" :course="course" />
                            <router-view @refresh="refreshTopics"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useAwarenessPreview } from '@/composables/awareness/useAwarenessPreview';
import LoadingSpinner from '@/components/General/LoadingSpinner.vue';
import CourseOverview from './CourseOverview.vue';
import SidebarManagement from './SidebarManagement.vue';

const {
    course,
    topics,
    loading,
    currentAwarenessPage,
    showCourseOverview,
    loadCourseData,
    refreshTopics,
    createCourseTopic,
    updateCourseTopic,
    deleteCourseTopic,
    deleteLesson
} = useAwarenessPreview();

const courseId = computed(() => currentAwarenessPage.courseId.value);

const handlers = computed(() => ({
  topicCreated: (topicData) => createCourseTopic(courseId.value, topicData),
  topicUpdated: (topic) => updateCourseTopic(courseId.value, topic.topicId, { title: topic.title }),
  topicDeleted: (topic) => deleteCourseTopic(courseId.value, topic._id),
  lessonDeleted: (payload) => deleteLesson(courseId.value, payload.topicId, payload.lesson)
}));

onMounted(() => {
    loadCourseData();
});
</script>