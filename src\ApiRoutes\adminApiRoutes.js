const BASE_URL = import.meta.env.VITE_API_URL;
const ApiUrl = `${BASE_URL}/api/secure/admin`;

const AdminApiRoutes = {
    AdminLogin: ApiUrl + "/auth/login",
    AdminLogout: ApiUrl + "/auth/logout",
    AdminTokenRefresh: `${ApiUrl}/token-refresh`,

    // General Profile routes
    AdminProfile: ApiUrl + "/general/me",
    AdminUpdateProfile: ApiUrl + "/general/profile/update",
    AdminChangePassword: ApiUrl + "/general/change/password",

    // Dashboard report routes - RESTORE ALL ORIGINAL CHARTS
    EvaluationSectorCharts: ApiUrl + "/dashboard/report/evaluations",
    FairDecisionDomainsCharts: ApiUrl + "/dashboard/report/fair-decision", // RESTORED
    RegisteredUsersCharts: ApiUrl + "/dashboard/report/registered-users",

    // Evaluation and Risk Factor routes - KEEP ALL ORIGINAL
    RiskFactorGetAll: ApiUrl + "/evaluation/risk-factor/get/all",
    RiskFactorManage: ApiUrl + "/evaluation/risk-factor/manage",
    EvaluationSectorGetAll: ApiUrl + "/evaluation/sector/get/all",
    EvaluationSectorManage: ApiUrl + "/evaluation/sector/manage",
    FdSectorSectorGetAll: ApiUrl + "/evaluation/fd/sector/get/all", // RESTORED
    FdSectorSectorManage: ApiUrl + "/evaluation/fd/sector/manage", // RESTORED
    QuestionGroupsGetAll: ApiUrl + "/evaluation/question/groups/get/all",
    QuestionGroupsManage: ApiUrl + "/evaluation/question/groups/manage",

    // RESTORE ALL AWARENESS EVALUATION ROUTES
    GetAwareness: ApiUrl + "/evaluation/awareness/list",
    GetAwarenessSingle: (course_id) => `${ApiUrl}/evaluation/awareness/single/${course_id}`,
    CreateAwareness: ApiUrl + "/evaluation/awareness/create",
    DeleteAwareness: (course_id) => `${ApiUrl}/evaluation/awareness/delete/${course_id}`,
    UpdateAwareness: (course_id) => `${ApiUrl}/evaluation/awareness/update/${course_id}`,
    GetCourseTopics: (course_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/list`,
    CreateCourseTopics: (course_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/create`,
    UpdateCourseTopics: (course_id, topic_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/update/${topic_id}`,
    DeleteCourseTopic: (course_id, topic_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/delete/${topic_id}`,
    CreateLesson: (course_id, topic_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/${topic_id}/lesson/create`,
    UpdateLesson: (course_id, topic_id, lesson_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/${topic_id}/lesson/update/${lesson_id}`,
    GetLesson: (course_id, topic_id, lesson_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/${topic_id}/lesson/single/${lesson_id}`,
    DeleteLesson: (course_id, topic_id, lesson_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/${topic_id}/lesson/delete/${lesson_id}`,
    CreateOrUpdateAwarenessQuestion: (course_id, topic_id, lesson_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/${topic_id}/lesson/${lesson_id}/question/create-or-update`,
    DeleteAwarenessQuestion: (course_id, topic_id, lesson_id, question_id) => `${ApiUrl}/evaluation/awareness/${course_id}/topic/${topic_id}/lesson/${lesson_id}/question/delete/${question_id}`,
    
    AwarenessEvaluationSections: ApiUrl + "/evaluation/awareness/section/list",
    AwarenessEvaluationSectionCreate: ApiUrl + "/evaluation/awareness/section/create",
    AwarenessEvaluationSectionUpdate: ApiUrl + "/evaluation/awareness/section/update",
    AwarenessEvaluationSectionDelete: ApiUrl + "/evaluation/awareness/section/delete",
    AwarenessEvaluationSectionQuestionManage: ApiUrl + "/evaluation/awareness/section/question/manage",
    AwarenessEvaluationSectionQuestionDelete: ApiUrl + "/evaluation/awareness/section/question/delete",

    // Question routes - KEEP ALL ORIGINAL (handles both risk and fairness)
    ListQuestions: ApiUrl + "/question/list",
    ManageQuestion: ApiUrl + "/question/manage",
    DeleteQuestion: ApiUrl + "/question/delete",
    QuestionTypes: ApiUrl + "/question/types",

    // User routes - KEEP ALL ORIGINAL
    Users: ApiUrl + "/user/list",
    CreateUser: ApiUrl + "/user/create",
    GetUser: ApiUrl + "/user/single",
    UpdateUser: ApiUrl + "/user/update",
    DeleteUser: ApiUrl + "/user/delete",

    // Workshop routes - KEEP ALL ORIGINAL
    Workshops: ApiUrl + "/workshop/list",
    CreateWorkshop: ApiUrl + "/workshop/create",
    GetWorkshop: ApiUrl + "/workshop/single",
    GetWorkshopCertificates: ApiUrl + "/workshop/certificates",
    UpdateWorkshop: ApiUrl + "/workshop/update",
    DeleteWorkshop: ApiUrl + "/workshop/delete",

    // Certification routes - KEEP ALL ORIGINAL
    EvaluationCertificates: ApiUrl + "/evaluation/certificate/list",
    EvaluationCertificateSettings: ApiUrl + "/evaluation/certificate/settings",
    EvaluationCertificateSettingsUpdate: ApiUrl + "/evaluation/certificate/settings/update",
    ParticipantCertificates: ApiUrl + "/participant/certificate/list",
    AwarenessCertificates: ApiUrl + "/awareness/certificate/list",

    // Media upload route - KEEP ORIGINAL
    MEDIA: ApiUrl + "/media/upload",

    // Pricing route - KEEP ALL ORIGINAL
    PricingCreate: ApiUrl + "/pricing/create",
    AdminPricingList: ApiUrl + "/pricing/list",
    PricingDelete: (id) => `${ApiUrl}/pricing/delete/${id}`,
    PricingSingle: (id) => `${ApiUrl}/pricing/single/${id}`,
    PricingUpdate: (id) => `${ApiUrl}/pricing/update/${id}`
};

export default AdminApiRoutes;