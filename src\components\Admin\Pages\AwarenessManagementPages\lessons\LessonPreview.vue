<template>
    <div class="w-100">
        <LoadingSpinner :loading="loading" />

        <div class="w-100" v-if="lesson">
            <div class="card question-holder">
                <div class="card-body p-3 p-lg-5">
                    <div class="row">
                        <div class="w-100"><h1>{{ lesson.title }}</h1></div>
                        <div class="w-100 mt-4 ql-editor" v-if="lesson.lesson_type === 'content'">
                            <div class="w-100" v-html="lesson.description"></div>
                        </div>
                    </div>

                    <div v-if="lesson.lesson_type !== 'content'" class="col-lg-12">
                        <div class="w-100 mt-5">
                            <QuestionAccordion :loading="loading" :questions="lesson.questions || []" @question-saved="handleQuestionSaved"  @question-deleted="handleQuestionDeleted"/>
                            <div class="w-100 mt-5">
                                <a class="btn btn-warning rounded-pill" href="javascript:void(0)" @click="handleQuestionAdded">  <i class="fa fa-fw fa-plus"></i> New Question </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useLessonPreview } from '@/composables/awareness/useLessonPreview';
import LoadingSpinner from '@/components/General/LoadingSpinner.vue';
import QuestionAccordion from './questions/QuestionAccordion.vue';

const { lesson, loading, handleQuestionSaved, handleQuestionDeleted, handleQuestionAdded, restartPage } = useLessonPreview();

onMounted(() => {
    restartPage();
});
</script>