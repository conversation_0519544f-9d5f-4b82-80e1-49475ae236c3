<!-- src/components/User/Pages/AuthPages/resetPassword.vue -->
<template>
    <div class="auth-page">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>

        <!-- Reset Password Modal -->
        <div class="reset-modal-overlay">
            <div class="reset-modal">
                <!-- Header -->
                <div class="modal-header">
                    <div class="logo-section">
                        <a href="https://raidot.ai" class="brand-logo" target="_blank" rel="noopener noreferrer">
                            <span class="logo-text">Rai<span class="logo-accent">DOT</span></span>
                        </a>
                    </div>
                    <h2 class="modal-title">Reset Password</h2>
                    <p class="modal-subtitle">Enter your new password to secure your account</p>
                </div>

                <!-- Success Message -->
                <div v-if="successMsg" class="success-message">
                    <div class="success-content">
                        <i class="fa fa-check-circle"></i>
                        <p>{{ successMsg }}</p>
                        <small>Redirecting to login...</small>
                    </div>
                </div>

                <!-- Reset Form -->
                <form v-else class="reset-form" @submit.prevent="resetPassword">
                    <div class="form-group">
                        <label class="form-label">New Password</label>
                        <div class="input-wrapper">
                            <i class="fa fa-lock input-icon"></i>
                            <input 
                                :type="showPassword ? 'text' : 'password'" 
                                v-model="password" 
                                class="form-input" 
                                :class="{ 'is-invalid': errors.password }"
                                placeholder="Enter new password"
                                autocomplete="new-password"
                                required
                            >
                            <button 
                                type="button" 
                                class="password-toggle"
                                @click="showPassword = !showPassword"
                            >
                                <i :class="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                            </button>
                        </div>
                        <!-- Password Strength Indicator -->
                        <div v-if="password" class="password-strength">
                            <div class="strength-meter">
                                <div 
                                    class="strength-bar" 
                                    :class="passwordStrength.class"
                                    :style="{ width: passwordStrength.score + '%' }"
                                ></div>
                            </div>
                            <span class="strength-text" :class="passwordStrength.class">
                                {{ passwordStrength.text }}
                            </span>
                        </div>
                        
                        <div class="error-report">
                            <small v-if="errors.password" class="text-danger">{{ errors.password }}</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Confirm Password</label>
                        <div class="input-wrapper">
                            <i class="fa fa-lock input-icon"></i>
                            <input 
                                :type="showConfirmPassword ? 'text' : 'password'" 
                                v-model="password_confirmation" 
                                class="form-input" 
                                :class="{ 'is-invalid': errors.password_confirmation }"
                                placeholder="Re-enter new password"
                                autocomplete="new-password"
                                required
                            >
                            <button 
                                type="button" 
                                class="password-toggle"
                                @click="showConfirmPassword = !showConfirmPassword"
                            >
                                <i :class="showConfirmPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                            </button>
                        </div>
                        <div class="error-report">
                            <small v-if="errors.password_confirmation" class="text-danger">{{ errors.password_confirmation }}</small>
                        </div>
                    </div>

                    <button 
                        type="submit" 
                        class="submit-btn"
                        :class="{ loading: loading }"
                        :disabled="loading"
                    >
                        <span v-if="!loading" class="btn-content">
                            <i class="fa fa-key"></i>
                            Reset Password
                        </span>
                        <span v-else class="btn-content">
                            <i class="fa fa-spinner fa-spin"></i>
                            Resetting...
                        </span>
                    </button>

                    <div class="modal-footer">
                        <p class="footer-text">
                            Remember your password? 
                            <router-link :to="{name: 'Login'}" class="auth-link">
                                Sign In
                            </router-link>
                        </p>
                    </div>
                </form>

                <!-- Security Badge -->
                <div class="security-badge">
                    <i class="fa fa-shield-alt"></i>
                    <span>Secure Password Reset</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export default {
    name: 'ResetPasswordPage',
    props: {
        resetCode: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            showPassword: false,
            showConfirmPassword: false,
            password: '',
            password_confirmation: '',
            errors: {},
            successMsg: ''
        };
    },
    computed: {
        passwordStrength() {
            if (!this.password) return { score: 0, text: '', class: '' };
            
            let score = 0;
            const password = this.password;
            
            // Length check
            if (password.length >= 8) score += 20;
            if (password.length >= 12) score += 10;
            
            // Character variety checks
            if (/[a-z]/.test(password)) score += 15;
            if (/[A-Z]/.test(password)) score += 15;
            if (/[0-9]/.test(password)) score += 15;
            if (/[^A-Za-z0-9]/.test(password)) score += 25;
            
            // Determine strength level
            let text = '';
            let className = '';
            
            if (score < 30) {
                text = 'Weak';
                className = 'weak';
            } else if (score < 60) {
                text = 'Fair';
                className = 'fair';
            } else if (score < 80) {
                text = 'Good';
                className = 'good';
            } else {
                text = 'Strong';
                className = 'strong';
            }
            
            return { score, text, class: className };
        }
    },
    methods: {
        resetPassword() {
            this.loading = true;
            this.errors = {};
            
            // Client-side validation
            if (this.password !== this.password_confirmation) {
                this.errors.password_confirmation = "Passwords do not match.";
                this.loading = false;
                return;
            }

            const data = {
                password: this.password,
                password_confirmation: this.password_confirmation,
                reset_code: this.resetCode
            };

            ApiService.POST(ApiRoutes.UserResetPassword, data, (res) => {
                if (res.status === 200) {
                    this.successMsg = res.msg;
                    setTimeout(() => {
                        this.$router.push({ name: 'Login' });
                    }, 2000);
                } else if (res.error) {
                    this.errors = res.error;
                }
                this.loading = false;
            });
        }
    },
    mounted() {
        window.scrollTo(0, 0);
    }
};
</script>

<style scoped>
.auth-page {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* Background Elements */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: -1s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: -3s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 20%;
    animation-delay: -2s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 10%;
    right: 30%;
    animation-delay: -4s;
}

.shape-5 {
    width: 60px;
    height: 60px;
    bottom: 10%;
    right: 10%;
    animation-delay: -0.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Modal Overlay */
.reset-modal-overlay {
    position: relative;
    z-index: 1;
    padding: 2rem;
    width: 100%;
    max-width: 500px;
}

/* Modal Container */
.reset-modal {
    background: white;
    border-radius: 24px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    position: relative;
    backdrop-filter: blur(10px);
}

/* Header */
.modal-header {
    text-align: center;
    margin-bottom: 2rem;
    flex-direction: column;
}

.logo-section {
    margin-bottom: 1.5rem;
}

.brand-logo {
    display: inline-block;
    text-decoration: none;
}

.logo-text {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
}

.logo-accent {
    color: #667eea;
}

.modal-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.modal-subtitle {
    color: #6b7280;
    font-size: 1rem;
    margin: 0;
}

/* Success Message */
.success-message {
    text-align: center;
    padding: 2rem 0;
}

.success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.success-content i {
    font-size: 3rem;
    color: #10b981;
}

.success-content p {
    font-size: 1.125rem;
    color: #1f2937;
    font-weight: 600;
    margin: 0;
}

.success-content small {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Form */
.reset-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.form-input.is-invalid {
    border-color: #ef4444;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #667eea;
}

.submit-btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.submit-btn.loading {
    pointer-events: none;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.modal-footer {
    text-align: center;
    margin-top: 1rem;
}

.footer-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.auth-link:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.security-badge {
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.75rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.security-badge i {
    color: #10b981;
}

.error-report {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
}

/* Password Strength Indicator */
.password-strength {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.strength-meter {
    flex: 1;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-bar.weak {
    background: linear-gradient(90deg, #ef4444, #f87171);
}

.strength-bar.fair {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.strength-bar.good {
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
}

.strength-bar.strong {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.strength-text {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.strength-text.weak {
    color: #ef4444;
}

.strength-text.fair {
    color: #f59e0b;
}

.strength-text.good {
    color: #3b82f6;
}

.strength-text.strong {
    color: #10b981;
}

/* Responsive Design */
@media (max-width: 768px) {
    .reset-modal-overlay {
        padding: 1rem;
    }
    
    .reset-modal {
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }
    
    .logo-text {
        font-size: 1.75rem;
    }
    
    .modal-title {
        font-size: 1.5rem;
    }
    
    .modal-subtitle {
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .reset-modal-overlay {
        padding: 0.5rem;
    }
    
    .reset-modal {
        padding: 1.5rem 1rem;
    }
    
    .logo-text {
        font-size: 1.5rem;
    }
    
    .modal-title {
        font-size: 1.25rem;
    }
    
    .security-badge {
        bottom: -0.5rem;
        font-size: 0.7rem;
        padding: 0.375rem 0.75rem;
    }
}

/* Focus states for accessibility */
.form-input:focus,
.submit-btn:focus,
.password-toggle:focus,
.auth-link:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .form-input {
        border-width: 3px;
    }
    
    .submit-btn {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .shape,
    .submit-btn,
    .form-input,
    .password-toggle,
    .auth-link {
        animation: none;
        transition: none;
    }
    
    @keyframes float {
        from, to {
            transform: translateY(0px) rotate(0deg);
        }
    }
}
</style>