<template>
    <div class="col-md-4">
      <div class="eachPrice bg-light shadow-lg py-5 px-5 border border-5 mb-4"  :class="[borderClass, { 'd-flex justify-content-start': userType === 'user' }]">
        <div v-if="currentPackage && currentPackage._id === pricing._id && userType === 'user'" class="active_status">
          Current Package
        </div>
        <!-- Header -->
        <div class="w-100">
          <h1 class="plan">{{ pricing.package_name }}</h1>
          <h4 class="price text-secondary">{{ pricing.package_price === 0 ? 'FREE' : `${pricing.currency} ${pricing.package_price}` }}</h4>
        </div>
  
        <!-- Features -->
        <div class="w-100 mt-4">
          <div class="features">
            <ul class="list-unstyled">
              <li class="text-start" v-for="(feature, index) in sortedFeatures" :key="index">
                <i class="fa fa-fw" :class="feature.included ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'"></i> {{ feature.text }}
              </li>
            </ul>
          </div>
        </div>
  
        <!-- Action Buttons -->
        <div v-if="userType === 'admin' || pricing.package_price !== 0" class="w-100 mt-5 d-flex gap-2">
          <button v-for="(btn, index) in generatedButtons" :key="index" class="btn" :class="btn.class" @click="btn.action">
            <span v-if="btn.loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            <span v-if="!btn.loading">{{ btn.text }}</span>
          </button>
        </div>
      </div>
    </div>
</template>
  
<script setup>
import { computed, ref } from "vue";

const currentPackage = ref(JSON.parse(localStorage.getItem("Subscription")));

const props = defineProps({
  pricing: { type: Object, required: true },
  generateButtons: { type: Function, required: true },
  borderClass: { type: String, default: "border-success" },
  userType: { type: String, default: "user" },
});

const formattedFeatures = computed(() => {
  const p = props.pricing;
  const features = [];

  features.push({ text: `Risk evaluation (${p.risk_evaluation?.sessions || 0} sessions)`, included: !!p.risk_evaluation?.status });
  features.push({ text: "Risk diagnosis included", included: !!p.risk_evaluation?.includes_diagnosis });

  features.push({ text: `Fair decision analysis (${p.fair_decision_analysis?.sessions || 0} sessions)`, included: !!p.fair_decision_analysis?.status });
  features.push({ text: "Fair decision diagnosis included", included: !!p.fair_decision_analysis?.includes_diagnosis });

  features.push({ text: "Eligible for in-person services", included: !!p.in_person_services?.status });
  features.push({ text: `In-person consultancy (${p.in_person_services?.consultancy_hours || 0} hours)`, included: !!p.in_person_services?.consultancy });
  features.push({ text: "In-person evaluation", included: !!p.in_person_services?.evaluation });
  features.push({ text: `In-person risk evaluation sessions: ${p.in_person_services?.risk_evaluation_sessions || 0}`, included: !!p.in_person_services?.risk_evaluation_sessions });
  features.push({ text: `In-person fair decision sessions: ${p.in_person_services?.fair_decision_sessions || 0}`, included: !!p.in_person_services?.fair_decision_sessions });

  features.push({ text: "Training included", included: !!p.training || !!p.in_person_services?.training });
  features.push({ text: `Online consultancy: ${p.online_consultancy_hours || 0} hours`, included: !!p.online_consultancy_hours });

  return features;
});

const sortedFeatures = computed(() => {
  if (!formattedFeatures.value) return [];
  const included = formattedFeatures.value.filter((f) => f.included);
  const notIncluded = formattedFeatures.value.filter((f) => !f.included);
  return [...included, ...notIncluded];
});

const generatedButtons = computed(() => {
  return props.generateButtons(props.pricing);
});
</script>
  
<style scoped>
  .eachPrice {
    border-radius: 12px;
  }
</style>