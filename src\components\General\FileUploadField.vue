<template>
  <div class="form-group mb-4">
    <div class="w-100" v-if="previewUrl">
      <img :src="previewUrl" :alt="label" class="img-fluid rounded-3 mb-2 bg-light p-2 shadow-sm border" style="max-height: 200px; max-width: 100%;">
    </div>
    <label class="form-label"> <strong>{{ label }}</strong> </label>
    <input type="file" class="form-control" :name="name" :accept="accept" @change="handleFileChange">
    <input type="hidden" :name="hiddenFieldName" :value="hiddenFieldValue">
    <div class="error-report text-danger"></div>
  </div>
</template>

<script setup>
defineProps({
  previewUrl: { type: String, default: null },
  label: { type: String, default: 'File Upload' },
  name: { type: String, default: 'file' },
  accept: { type: String, default: 'image/*' },
  hiddenFieldName: { type: String, default: 'file_path' },
  hiddenFieldValue: { type: String, default: '' }
});

const emit = defineEmits(['change']);

const handleFileChange = (event) => {
  emit('change', event);
};
</script>