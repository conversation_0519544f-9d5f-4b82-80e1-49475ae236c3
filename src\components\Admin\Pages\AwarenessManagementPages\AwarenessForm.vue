<template>
    <div class="container-lg">
        <div class="w-100">
            <div class="row">
                <div class="col-xl-10 offset-xl-1 col-md-12">
                    <form class="w-100" @submit.prevent="handleSubmit">
                        <FormCard :title="isEditMode ? 'Update Awareness Course' : 'New Awareness Course'" :cancel-route="{ name: 'Awareness' }" submit-text="Save Changes">
                            <div class="row" v-if="!isEditMode || courseData">
                                <div class="col-lg-12">
                                    <FileUploadField
                                        :preview-url="formData.banner_full_path"
                                        label="Course Icon"
                                        name="file"
                                        accept="image/*"
                                        :hidden-field-value="formData.banner"
                                        hidden-field-name="banner"
                                        @change="handleFileUpload"
                                    />
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group mb-4">
                                        <label class="form-label"><strong>Title</strong></label>
                                        <input type="text" class="form-control" name="title" v-model="formData.title" placeholder="Title">
                                        <div class="error-report text-danger"></div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group mb-4">
                                        <label class="form-label"><strong>Description</strong></label>
                                        <textarea class="form-control" name="description" v-model="formData.description" placeholder="Course short description"></textarea>
                                        <div class="error-report text-danger"></div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group mb-4">
                                        <label class="form-label"><strong>Course Package Level</strong></label>
                                        <select class="form-select" name="package_id" v-model="formData.selected_package">
                                            <option v-if="isEditMode && formData.selected_package" :value="formData.selected_package"> {{ formData.selected_package.package_name }} </option>
                                            <option v-if="!isEditMode" :value="null" disabled selected>Select Package</option>
                                            <option v-for="pkg in availablePackages" :key="pkg._id" :value="pkg"> {{ pkg.package_name }} </option>
                                        </select>
                                        <div class="error-report text-danger"></div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group mt-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="issueCertificate" v-model="formData.issue_certificate" true-value="1" false-value="0">
                                            <label class="form-check-label" for="issueCertificate">Issue Certificate</label>
                                        </div>
                                        <div class="error-report text-danger"></div>
                                    </div>
                                </div>
                            </div>
                        </FormCard>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { usePricingActions } from '@/composables/pricing/usePricingActions';
import { useFileUpload } from '@/composables/general/useFileUpload';
import FormCard from '@/components/General/FormCard.vue';
import FileUploadField from '@/components/General/FileUploadField.vue';

const props = defineProps({
    isEditMode: {
        type: Boolean,
        default: false
    },
    courseData: {
        type: Object,
        default: null
    }
});

const emit = defineEmits(['submit']);

const { pricing_packages, getAllPricing } = usePricingActions();
const { attachFile } = useFileUpload();

const formData = ref({
    title: '',
    slug: '',
    description: '',
    banner: null,
    banner_full_path: null,
    selected_package: {},
    issue_certificate: '0',
});

const availablePackages = computed(() => {
    if (!props.isEditMode) return pricing_packages.value;
    if (!formData.value?.selected_package) return pricing_packages.value;
    return pricing_packages.value.filter( pkg => pkg._id !== formData.value.selected_package._id);
});

const handleSubmit = () => {
    emit('submit', formData.value);
};

const handleFileUpload = async (event) => {
    await attachFile(event, formData.value);
};

watch(() => props.courseData, (newCourseData) => {
    if (newCourseData && props.isEditMode) {
        formData.value = { ...newCourseData };
    }
}, { immediate: true, deep: true });

onMounted(() => {
    getAllPricing('admin');
    if (!props.isEditMode) {
        formData.value = {
            title: '',
            slug: '',
            description: '',
            banner: null,
            banner_full_path: null,
            selected_package: {},
            issue_certificate: '0',
        };
    }
});
</script>
