<!-- src/components/User/Pages/AuthPages/login.vue -->
<template>
    <div class="auth-page">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>

        <div class="auth-container">
            <!-- Left Side - Branding -->
            <div class="auth-branding">
                <div class="branding-content">
                    <div class="logo-section">
                        <a href="https://raidot.ai" class="brand-logo" target="_blank" rel="noopener noreferrer">
                            <span class="logo-text">Rai<span class="logo-accent">DOT</span></span>
                        </a>
                        <div class="logo-subtitle">AI Risk & Fairness Platform</div>
                    </div>
                    
                    <div class="welcome-section">
                        <h1 class="welcome-title">Welcome Back!</h1>
                        <p class="welcome-description">
                            Sign in to continue your AI risk evaluation and fairness analysis journey
                        </p>
                    </div>

                    <div class="features-list">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-shield"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Risk Evaluation</h3>
                                <p>Comprehensive AI risk assessment tools</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-balance-scale"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Fairness Analysis</h3>
                                <p>Ensure ethical AI decision making</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fa fa-graduation-cap"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Training & Certification</h3>
                                <p>Learn and get certified in AI best practices</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="auth-form-section">
                <div class="form-container">
                    <div class="form-header">
                        <h2 class="form-title">Sign In</h2>
                        <p class="form-subtitle">Enter your credentials to access your account</p>
                        <!-- Error display section -->
                        <div v-if="loginError" class="error-alert">
                            <i class="fa fa-exclamation-circle"></i>
                            <span>{{ loginError }}</span>
                        </div>
                        <div v-if="successMessage" class="success-alert">
                            <i class="fa fa-check-circle"></i>
                            <span>{{ successMessage }}</span>
                        </div>
                    </div>

                    <form class="auth-form" @submit.prevent="Login">
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <div class="input-wrapper">
                                <i class="fa fa-envelope input-icon"></i>
                                <input 
                                    type="email" 
                                    v-model="LoginForm.email" 
                                    class="form-input" 
                                    placeholder="Enter your email"
                                    required
                                >
                            </div>
                            <div class="error-report text-danger"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Password</label>
                            <div class="input-wrapper">
                                <i class="fa fa-lock input-icon"></i>
                                <input 
                                    :type="showPassword ? 'text' : 'password'" 
                                    v-model="LoginForm.password" 
                                    class="form-input" 
                                    placeholder="Enter your password"
                                    autocomplete="current-password"
                                    required
                                >
                                <button 
                                    type="button" 
                                    class="password-toggle"
                                    @click="showPassword = !showPassword"
                                >
                                    <i :class="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                                </button>
                            </div>
                            <div class="error-report text-danger"></div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-wrapper">
                                <input 
                                    type="checkbox" 
                                    @change="LoginForm.remember = !LoginForm.remember"
                                    class="checkbox-input"
                                >
                                <span class="checkbox-custom"></span>
                                <span class="checkbox-label">Remember me</span>
                            </label>
                            
                            <router-link :to="{name: 'ForgotPassword'}" class="forgot-link">
                                Forgot Password?
                            </router-link>
                        </div>

                        <input type="hidden" name="g-recaptcha-response" v-model="LoginForm['g-recaptcha-response']" id="g-recaptcha-response-v3">

                        <button 
                            type="submit" 
                            class="submit-btn"
                            :class="{ loading: loading }"
                            :disabled="loading"
                        >
                            <span v-if="!loading" class="btn-content">
                                <i class="fa fa-sign-in"></i>
                                Sign In
                            </span>
                            <span v-else class="btn-content">
                                <i class="fa fa-spinner fa-spin"></i>
                                Signing In...
                            </span>
                        </button>

                        <div class="form-divider">
                            <span>or</span>
                        </div>

                        <div class="auth-footer">
                            <p class="footer-text">
                                Don't have an account? 
                                <router-link :to="{name: 'Register'}" class="auth-link">
                                    Create Account
                                </router-link>
                            </p>
                        </div>
                    </form>
                </div>

             
            </div>
        </div>

        <!-- Mobile Header (visible on mobile only) -->
        <div class="mobile-header">
            <a href="https://raidot.ai" class="mobile-logo" target="_blank" rel="noopener noreferrer">
                <span class="logo-text">Rai<span class="logo-accent">DOT</span></span>
            </a>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { usePricingActions } from '@/composables/pricing/usePricingActions';
const { getSubscription } = usePricingActions();

export default {
    name: 'LoginPage',
    data() {
        return {
            loading: false,
            showPassword: false,
            loginError: '', 
            successMessage: '',
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            LoginForm: {
                email: '',
                password: '',
                remember: false,
                'g-recaptcha-response': ''
            }
        }
    },
    methods: {
        // Complete Login method with comprehensive error handling
        async Login() {
            this.loading = true;
            this.loginError = '';
            ApiService.ClearErrorHandler();

            try {
                const res = await new Promise((resolve, reject) => {
                    ApiService.POST(ApiRoutes.UserLogin, this.LoginForm, (response) => {
                        if (parseInt(response.status) === 200) {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    });
                });

                const { status, msg, token, user } = res;
                localStorage.setItem('JwtToken', token);
                localStorage.setItem('UserInfo', JSON.stringify(user));

                const subscriptionData = await getSubscription();
                localStorage.setItem('Subscription', JSON.stringify(subscriptionData.package));

                // Optional: Show success message
                this.successMessage = 'Login successful! Redirecting...';
                // Small delay to show success message
                setTimeout(() => {
                    const intended = localStorage.getItem('UserIntendedRoute');
                    if (intended) {
                        localStorage.removeItem('UserIntendedRoute');
                        this.$router.push(intended);
                    } else {
                        this.$router.push({ name: 'Dashboard' });
                    }
                }, 1000);

            } catch (error) {
                console.error('Login error:', error);
                
                // Handle different types of errors with specific messages
                if (error.error) {
                    // Handle validation errors from ApiService.ErrorHandler
                    ApiService.ErrorHandler(error.error);
                    
                    // Set specific error messages based on common scenarios
                    if (error.error.email) {
                        this.loginError = 'Please enter a valid email address.';
                    } else if (error.error.password) {
                        this.loginError = 'Please enter your password.';
                    } else if (error.error.error) {
                        // Use server-provided error message
                        this.loginError = error.error.error;
                    } else {
                        this.loginError = 'Invalid email or password. Please try again.';
                    }
                } else if (error.message) {
                    this.loginError = error.message;
                } else if (error.status === 401) {
                    this.loginError = 'Invalid email or password. Please check your credentials.';
                } else if (error.status === 422) {
                    this.loginError = 'Please check your email and password format.';
                } else if (error.status === 429) {
                    this.loginError = 'Too many login attempts. Please try again later.';
                } else if (error.status >= 500) {
                    this.loginError = 'Server error. Please try again later.';
                } else if (error.status === 0 || !navigator.onLine) {
                    this.loginError = 'Network error. Please check your internet connection.';
                } else {
                    this.loginError = 'Login failed. Please check your credentials and try again.';
                }
            }

            this.loading = false;
        }
    },
    created() {
        if (this.UserInfo != null) {
            this.$router.push({ name: 'Dashboard' });
        }
    },
    mounted() {
        window.scrollTo(0, 0);
        
        // Load reCAPTCHA
        if (window.grecaptcha) {
            grecaptcha.ready(() => {
                grecaptcha.execute(import.meta.env.VITE_RECAPTCHA_SITE_KEY, { action: 'submit' }).then(token => {
                    this.LoginForm['g-recaptcha-response'] = token;
                    const target = document.getElementById('g-recaptcha-response-v3');
                    if (target) target.value = token;
                });
            });
        }
    }
}
</script>

<style scoped>
.auth-page {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* Background Elements */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: -1s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: -3s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 20%;
    animation-delay: -2s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 10%;
    right: 30%;
    animation-delay: -4s;
}

.shape-5 {
    width: 60px;
    height: 60px;
    bottom: 10%;
    right: 10%;
    animation-delay: -0.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Main Container */
.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1200px;
    width: 100%;
    margin: 2rem;
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    position: relative;
    z-index: 1;
    min-height: 600px;
}

/* Left Side - Branding */
.auth-branding {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.auth-branding::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.branding-content {
    width: 100%;
    position: relative;
    z-index: 1;
}

.logo-section {
    margin-bottom: 3rem;
}

.brand-logo {
    display: inline-block;
    text-decoration: none;
}

.logo-text {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
}

.logo-accent {
    color: #ffd700;
}

.logo-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.welcome-section {
    margin-bottom: 3rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.welcome-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    backdrop-filter: blur(10px);
}

.feature-content h3 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.feature-content p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
}

/* Right Side - Form */
.auth-form-section {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.form-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.form-subtitle {
    color: #6b7280;
    font-size: 1rem;
    margin: 0;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.form-input.is-invalid {
    border-color: #ef4444;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #667eea;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label {
    font-size: 0.875rem;
    color: #374151;
}

.forgot-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-link:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.submit-btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.submit-btn.loading {
    pointer-events: none;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.form-divider {
    position: relative;
    text-align: center;
    margin: 0.5rem 0;
}

.form-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
    z-index: 1;
}

.form-divider span {
    background: white;
    color: #9ca3af;
    padding: 0 1rem;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.auth-footer {
    text-align: center;
}

.footer-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.auth-link:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.security-badge {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.security-badge i {
    color: #059669;
}

/* Mobile Header */
.mobile-header {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.mobile-logo {
    text-decoration: none;
}

.mobile-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1f2937;
}

.mobile-logo .logo-accent {
    color: #667eea;
}

/* Error States */
.error-report {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .auth-container {
        margin: 1rem;
        max-width: 900px;
    }
    
    .auth-branding,
    .auth-form-section {
        padding: 2rem;
    }
    
    .features-list {
        gap: 1rem;
    }
    
    .feature-item {
        gap: 0.75rem;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .auth-page {
        padding-top: 80px;
    }
    
    .mobile-header {
        display: block;
    }
    
    .auth-container {
        grid-template-columns: 1fr;
        margin: 1rem 0.5rem;
        min-height: auto;
    }
    
    .auth-branding {
        padding: 2rem 1.5rem 1.5rem;
        text-align: center;
    }
    
    .welcome-section {
        margin-bottom: 2rem;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .features-list {
        flex-direction: row;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
        max-width: 120px;
    }
    
    .feature-content h3 {
        font-size: 0.875rem;
    }
    
    .feature-content p {
        font-size: 0.75rem;
    }
    
    .auth-form-section {
        padding: 2rem 1.5rem;
    }
    
    .security-badge {
        position: relative;
        bottom: auto;
        left: auto;
        transform: none;
        margin-top: 2rem;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .auth-container {
        margin: 0.5rem;
        border-radius: 16px;
    }
    
    .auth-branding,
    .auth-form-section {
        padding: 1.5rem 1rem;
    }
    
    .logo-text {
        font-size: 2rem;
    }
    
    .welcome-title {
        font-size: 1.25rem;
    }
    
    .welcome-description {
        font-size: 1rem;
    }
    
    .features-list {
        gap: 1rem;
    }
    
    .feature-item {
        max-width: 100px;
    }
    
    .form-title {
        font-size: 1.5rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

/* Focus states for accessibility */
.form-input:focus,
.submit-btn:focus,
.checkbox-wrapper:focus-within,
.forgot-link:focus,
.auth-link:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .form-input {
        border-width: 3px;
    }
    
    .submit-btn {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .shape,
    .submit-btn,
    .form-input,
    .checkbox-custom,
    .password-toggle,
    .forgot-link,
    .auth-link {
        animation: none;
        transition: none;
    }
    
    @keyframes float {
        from, to {
            transform: translateY(0px) rotate(0deg);
        }
    }
}
/* Error Alert Styles */
.error-alert {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 1px solid #fecaca;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #dc2626;
    font-size: 0.875rem;
    animation: slideDown 0.3s ease-out;
}

.error-alert i {
    font-size: 1rem;
    flex-shrink: 0;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced error states for form inputs */
.form-input.is-invalid {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.form-input.is-invalid:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

/* Error text styling */
.error-report {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
    font-weight: 500;
}

.text-danger {
    color: #dc2626;
}
.success-alert {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border: 1px solid #bbf7d0;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #16a34a;
    font-size: 0.875rem;
    animation: slideDown 0.3s ease-out;
}
</style>