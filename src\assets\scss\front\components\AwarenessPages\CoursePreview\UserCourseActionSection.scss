.course-actions {
  margin-top: 2rem;
}

/* Action Sections */
.getting-started,
.continue-learning,
.course-completed {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e5e7eb;
}

.getting-started-title,
.continue-title,
.completed-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.getting-started-title i {
  color: #3b82f6;
}

.continue-title i {
  color: #f59e0b;
}

.completed-title i {
  color: #fbbf24;
}

.getting-started-text,
.continue-text,
.completed-text {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.start-learning-btn,
.continue-btn,
.certificate-btn {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
}

.start-learning-btn:hover,
.continue-btn:hover,
.certificate-btn:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.certificate-btn {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.certificate-btn:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .getting-started,
  .continue-learning,
  .course-completed {
    padding: 1.5rem;
  }

  .getting-started-title,
  .continue-title,
  .completed-title {
    font-size: 1.25rem;
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .start-learning-btn,
  .continue-btn,
  .certificate-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }
}
