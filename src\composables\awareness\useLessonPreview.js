import { ref, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAwarenessManagementActions } from './useAwarenessManagementActions';

export const useLessonPreview = () => {
  const route = useRoute();
  const {
    currentLesson: lesson,
    getLesson,
    createOrUpdateQuestion,
    deleteQuestion,
    addNewQuestion,
    loading: managementLoading
  } = useAwarenessManagementActions();

  const course_id = ref(null);
  const topic_id = ref(null);
  const lesson_id = ref(null);

  const courseId = computed(() => course_id.value);
  const topicId = computed(() => topic_id.value);
  const lessonId = computed(() => lesson_id.value);

  const loadLesson = async () => {
    if (!course_id.value || !topic_id.value || !lesson_id.value) return;
    await getLesson(course_id.value, topic_id.value, lesson_id.value);
  };

  const handleQuestionSaved = async (payload) => {
    await createOrUpdateQuestion(
      payload.courseId || course_id.value,
      payload.topicId || topic_id.value,
      payload.lessonId || lesson_id.value,
      payload.question,
      payload.index
    );
  };

  const handleQuestionDeleted = async (payload) => {
    if (payload.question._id === null) {
      if (lesson.value && lesson.value.questions) {
        lesson.value.questions.splice(payload.index, 1);
        lesson.value.questions = [...lesson.value.questions];
      }
    } else {
      await deleteQuestion(
        payload.courseId || course_id.value,
        payload.topicId || topic_id.value,
        payload.lessonId || lesson_id.value,
        payload.question._id
      );

      if (lesson.value && lesson.value.questions) {
        lesson.value.questions.splice(payload.index, 1);
        lesson.value.questions = [...lesson.value.questions];
      }
    }
  };

  const handleQuestionAdded = () => {
    addNewQuestion();
  };

  const updateRouteParams = () => {
    course_id.value = route.params.course_id;
    topic_id.value = route.params.topic_id;
    lesson_id.value = route.params.lesson_id;
  };

  const restartPage = () => {
    updateRouteParams();
    loadLesson();
  };

  watch(() => route.params.lesson_id, (newId, oldId) => {
    if (newId !== oldId) {
      restartPage();
    }
  });

  return {
    lesson,
    loading: managementLoading,
    courseId,
    topicId,
    lessonId,
    loadLesson,
    handleQuestionSaved,
    handleQuestionDeleted,
    handleQuestionAdded,
    restartPage
  };
};