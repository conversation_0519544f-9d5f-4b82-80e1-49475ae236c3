.bg-danger, .bg-high {
    background-color: #ff002f !important;
    color: #ffffff;
}

.text-danger {
    color: #ff002f !important;
}

.border-danger {
    border-color: darken(#ff002f, 10%) !important;
}

.bg-limited {
    background-color: #778ca3 !important;
    color: #ffffff;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #222222;
}

.border-warning {
    border-color: #ffc107 !important;
}

.bg-success, .bg-low {
    background-color: #44bd32 !important;
    color: #ffffff;
}

.bg-bronze {
    background-color: #cd7f32;
    color: white;
}
.bg-silver {
    background-color: #c0c0c0;
    color: black;
}
.bg-gold {
    background-color: #ffd700;
    color: black;
}

.text-success{
    color: #44bd32 !important;
}

.border-success{
    border-color: #44bd32 !important;
}

.hover-opacity{
    opacity: 0.8;
    transform: scale(1);
    @include transition(all 0.1s linear);
    &:hover,&:active{
        opacity: 1;
        transform: scale(1.1);
        @include transition(all 0.1s linear);
    }
}

.custom-radio-checkbox{
    .form-check{
        .form-check-input-custom{
            appearance: none;
            border-radius:50%;
            height: 25px;
            width: 25px;
            vertical-align: sub;
            border: 2px solid #9d9d9d;
            &:checked {
                background-color: #007bff;
                border: 2px solid #007bff;
            }
        }
        .form-check-label{
            font-size: 17px;
            padding-left: 10px;
            padding-right: 15px;
            font-weight: bold;
        }
    }
}

.pointer-event-none{
    pointer-events: none;
}

.disable-element{
    pointer-events: none;
    opacity: 0.7;
}
