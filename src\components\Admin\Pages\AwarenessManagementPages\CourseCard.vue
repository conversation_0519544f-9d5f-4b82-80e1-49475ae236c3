<template>
  <div class="col-xl-3 col-lg-4 col-md-6">
    <div class="eachUser w-100 bg-white shadow mb-4">
      <div class="w-100 px-2 pt-3 d-flex flex-column justify-content-between" style="min-height: 350px">
        <div class="w-100 py-2 text-center">
          <div class="w-100 text-center" v-if="course.banner_full_path != null">
            <img 
              :src="course.banner_full_path" 
              alt="Banner" 
              class="img-fluid rounded-3 mb-2 bg-light p-2 shadow-sm border" 
              style="max-height: 200px; max-width: 100%;"
            >
          </div>
          <span class="badge bg-success me-2" v-if="course.package_name"> {{ course.package_name.toUpperCase() }} </span>
          <br><br>
          <router-link class="fs-5 text-secondary text-decoration-none" :to="{ name: 'AwarenessPreview', params: { course_id: course._id } }" >
            <strong>{{ truncateText(course.title, 20) }}</strong>
          </router-link>
          <p class="mt-2">{{ truncateText(course.description, 50) }}</p>
        </div>
        <div class="w-100 d-flex justify-content-between align-items-center p-2">
          <router-link :to="{ name: 'AwarenessEdit', params: { course_id: course._id } }" class="w-100 btn btn-sm btn-outline-primary rounded-pill me-1">Edit</router-link>
          <router-link :to="{ name: 'AwarenessPreview', params: { course_id: course._id } }" class="w-100 btn btn-sm btn-outline-success rounded-pill me-1">View</router-link>
          <a @click="$emit('delete', course)"  class="w-100 btn btn-sm btn-outline-danger rounded-pill">Delete</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  course: {
    type: Object,
    required: true
  }
});

defineEmits(['delete']);

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};
</script>
