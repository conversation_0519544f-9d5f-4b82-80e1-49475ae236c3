/* <PERSON><PERSON> Header */
.lesson-header {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.lesson-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: #6b7280;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #059669;
}

.breadcrumb-separator {
    color: #9ca3af;
    margin: 0 0.25rem;
}

.breadcrumb-current {
    color: #1f2937;
    font-weight: 500;
}

.lesson-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.lesson-type-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.lesson-badge {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
    color: #1d4ed8;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.quiz-badge {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.completion-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #059669;
    font-size: 0.875rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lesson-header {
        padding: 1.5rem;
    }
}

/* Enhanced Animations */
.lesson-header {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .lesson-header {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .lesson-header {
        animation: none;
    }
}
