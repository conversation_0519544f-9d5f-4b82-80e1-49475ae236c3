<template>
    <div class="w-100">
        <form class="w-100" @submit.prevent="handleSubmit">
            <FormCard :title="isEditMode ? 'Update Lesson' : 'New Lesson'" :cancel-route="{ name: 'AwarenessPreview', params: { course_id: courseId } }" submit-text="Save Changes" >
                <div class="row" v-if="!isEditMode || lessonData">
                    <div class="col-lg-12">
                        <div class="form-group mb-4">
                            <label class="form-label"><strong>Title</strong></label>
                            <input type="text" class="form-control" name="title" v-model="formData.title" placeholder="Title" required>
                            <div class="error-report text-danger"></div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group mb-4">
                            <label class="form-label"><strong>Lesson Type</strong></label>
                            <select class="form-select" name="lesson_type" v-model="formData.lesson_type" @change="formData.lesson_type !== 'content' ? formData.description = '' : null">
                                <option value="content">Content Type Lesson</option>
                                <option value="quiz">Topic Quiz</option>
                                <option value="final_exam">Final Exam</option>
                            </select>
                            <div class="error-report text-danger"></div>
                        </div>
                    </div>
                    <div class="col-lg-6"></div>
                    <div class="col-lg-12" v-if="formData.lesson_type === 'content'">
                        <div class="form-group mb-4">
                            <label class="form-label"><strong>Description</strong></label>
                            <div class="w-100">
                                <QuillEditor theme="snow" :toolbar="QUILL_TOOLBAR_OPTIONS" v-model:content="formData.description" contentType="html"/>
                            </div>
                            <div class="error-report text-danger"></div>
                        </div>
                    </div>
                </div>
            </FormCard>
        </form>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { QUILL_TOOLBAR_OPTIONS } from '@/composables/general/useQuillEditor';
import { QuillEditor } from '@vueup/vue-quill';
import FormCard from '@/components/General/FormCard.vue';

const props = defineProps({
    isEditMode: { type: Boolean, default: false },
    lessonData: { type: Object, default: null },
    courseId: { type: String, required: true }
});

const emit = defineEmits(['submit']);

const DEFAULT_FORM_DATA = {
    title: '',
    description: '',
    lesson_type: 'content'
};

const formData = ref({ ...DEFAULT_FORM_DATA });

const handleSubmit = () => {
    emit('submit', formData.value);
};

watch(() => props.lessonData, (newLessonData) => {
    if (newLessonData && props.isEditMode) formData.value = { ...newLessonData };
}, { immediate: true, deep: true });

watch(() => props.isEditMode, (isEdit) => {
    if (!isEdit) formData.value = { ...DEFAULT_FORM_DATA };
}, { immediate: true });
</script>
