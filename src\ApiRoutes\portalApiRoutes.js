const BASE_URL = import.meta.env.VITE_API_URL;
const ApiUrl = `${BASE_URL}/api/portal`;

const PortalApiRoutes = {
    // Profile management
    ProfileUpdate: ApiUrl + "/profile/update",
    ProfileUpdatePassword: ApiUrl + "/profile/update-password",

    // General user evaluation routes (shared between risk and fairness)
    UserEvaluations: ApiUrl + "/user/evaluation/all",
    UserEvaluationDelete: ApiUrl + "/user/evaluation/delete",
    UserEvaluationReport: ApiUrl + "/user/evaluation/report",
    UserEvaluationDetails: ApiUrl + "/user/evaluation/details",
    UserEvaluationQuestionSingle: ApiUrl + "/user/evaluation/question/single",
    UserEvaluationQuestionSingleChatGPT: ApiUrl + "/user/evaluation/question/single/chatGPT",
    UserEvaluationQuestionSingleUpdate: ApiUrl + "/user/evaluation/question/single/update",
    UserEvaluationCertificate: ApiUrl + "/user/evaluation/certificate",

    // Risk Evaluation specific routes
    RiskEvaluationSubmit: ApiUrl + "/risk/evaluation/submit",
    EvaluationTechnicalQuestions: ApiUrl + "/risk/evaluation/technical/questions",
    EvaluationGroups: ApiUrl + "/risk/evaluation/question/groups",
    EvaluationNonTechnicalQuestions: ApiUrl + "/risk/evaluation/non-technical/questions",
    EvaluationSectors: ApiUrl + "/risk/evaluation/sectors",
    
    // Risk specific user evaluation routes
    UserRiskEvaluations: ApiUrl + "/user/risk/evaluation/all",
    UserRiskEvaluationDelete: ApiUrl + "/user/risk/evaluation/delete",
    UserRiskEvaluationReport: ApiUrl + "/user/risk/evaluation/report",
    UserRiskEvaluationDetails: ApiUrl + "/user/risk/evaluation/details",
    UserRiskEvaluationQuestionSingle: ApiUrl + "/user/risk/evaluation/question/single",
    UserRiskEvaluationQuestionSingleChatGPT: ApiUrl + "/user/risk/evaluation/question/single/chatGPT",
    UserRiskEvaluationQuestionSingleUpdate: ApiUrl + "/user/risk/evaluation/question/single/update",
    UserRiskEvaluationCertificate: ApiUrl + "/user/risk/evaluation/certificate",
};

export default PortalApiRoutes;