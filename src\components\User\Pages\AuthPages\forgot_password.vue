<template>
    <div class="modal-overlay">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
            </div>
        </div>

        <div class="modal-container">
            <div class="modal-content">
                <!-- Header -->
                <div class="modal-header">
                    <div class="icon-wrapper">
                        <i class="fa fa-key"></i>
                    </div>
                    <h2 class="modal-title">Forgot Password?</h2>
                    <p class="modal-subtitle">Enter your email and we'll send you a reset link</p>
                </div>

                <!-- Success Message -->
                <div v-if="successMsg" class="success-alert">
                    <i class="fa fa-check-circle"></i>
                    <span>{{ successMsg }}</span>
                </div>

                <!-- Form -->
                <form class="modal-form" @submit.prevent="handleSubmit" v-if="!successMsg">
                    <div class="form-group">
                        <label class="form-label">Email Address</label>
                        <div class="input-wrapper">
                            <i class="fa fa-envelope input-icon"></i>
                            <input 
                                type="email" 
                                v-model="email" 
                                class="form-input" 
                                :class="{ 'is-invalid': errors.email }"
                                placeholder="Enter your email"
                                required
                            >
                        </div>
                        <div v-if="errors.email" class="error-message">{{ errors.email }}</div>
                    </div>

                    <button 
                        type="submit" 
                        class="submit-btn"
                        :class="{ loading: loading }"
                        :disabled="loading"
                    >
                        <span v-if="!loading" class="btn-content">
                            <i class="fa fa-paper-plane"></i>
                            Send Reset Link
                        </span>
                        <span v-else class="btn-content">
                            <i class="fa fa-spinner fa-spin"></i>
                            Sending...
                        </span>
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="modal-footer" v-if="!successMsg">
                    <p class="footer-text">
                        Remember your password? 
                        <button @click="goToLogin" class="text-link">Sign In</button>
                    </p>
                </div>

                <!-- Success Footer -->
                <div class="modal-footer" v-else>
                    <button @click="goToLogin" class="back-btn">
                        <i class="fa fa-arrow-left"></i>
                        Back to Sign In
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { useRouter } from 'vue-router';

export default {
    name: 'ForgotPasswordModal',
    data() {
        return {
            email: '',
            errors: {},
            successMsg: '',
            loading: false
        };
    },
    setup() {
        const router = useRouter();
        return { router };
    },
    methods: {
        async handleSubmit() {
            this.loading = true;
            this.successMsg = '';
            this.errors = {};
            
            try {
                const dummyCode = 'dummy-reset-code';
                const resolvedRoute = this.router.resolve({
                    name: 'ResetPassword',
                    params: { resetCode: dummyCode }
                });
                const baseUrl = window.location.origin;
                const resetUrlBase = resolvedRoute.href.replace(dummyCode, '');
                const resetUrl = baseUrl + resetUrlBase;

                ApiService.POST(ApiRoutes.UserForgotPassword, {
                    email: this.email, 
                    resetUrl: resetUrl
                }, (res) => {
                    if (res.status === 200) {
                        this.successMsg = res.msg;
                        this.email = '';
                    } else if (res.status === 422 && res.error) {
                        this.errors = res.error;
                    }
                });
            } catch (error) {
                console.error('Forgot password error:', error);
            }
            
            this.loading = false;
        },
        goToLogin() {
            this.$router.push({ name: 'Login' });
        }
    },
    mounted() {
        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
    },
    beforeUnmount() {
        // Restore body scroll
        document.body.style.overflow = 'auto';
    }
};
</script>

<style scoped>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

/* Background Elements */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 60px;
    height: 60px;
    top: 20%;
    left: 15%;
    animation-delay: -1s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    top: 60%;
    right: 20%;
    animation-delay: -3s;
}

.shape-3 {
    width: 40px;
    height: 40px;
    bottom: 30%;
    left: 25%;
    animation-delay: -2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(20px) rotate(240deg); }
}

.modal-container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 450px;
}

.modal-content {
    background: white;
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    text-align: center;
    margin-bottom: 2rem;
    flex-direction: column;
}

.icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.modal-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.modal-subtitle {
    color: #6b7280;
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.success-alert {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

.success-alert i {
    font-size: 1.25rem;
}

.modal-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.form-input.is-invalid {
    border-color: #ef4444;
}

.error-message {
    color: #ef4444;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.25rem;
}

.submit-btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.modal-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.footer-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.text-link {
    background: none;
    border: none;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.2s ease;
}

.text-link:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #f3f4f6;
    color: #374151;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #e5e7eb;
    transform: translateX(-2px);
}

/* Responsive Design */
@media (max-width: 480px) {
    .modal-content {
        padding: 2rem 1.5rem;
        margin: 0.5rem;
        border-radius: 20px;
    }
        
    .icon-wrapper {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }
    
    .modal-title {
        font-size: 1.5rem;
    }
    
    .modal-subtitle {
        font-size: 0.875rem;
    }
}

/* Focus states for accessibility */
.form-input:focus,
.submit-btn:focus,
.text-link:focus,
.back-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .modal-content,
    .shape,
    .submit-btn,
    .form-input,
    .back-btn {
        animation: none;
        transition: none;
    }
    
    @keyframes float {
        from, to {
            transform: translateY(0px) rotate(0deg);
        }
    }
}
</style>