import Pricing from '@/components/User/Pages/PricingPages/pricing.vue'
import SubscribeSuccess from '@/components/User/Pages/PricingPages/subscribe-success.vue'
import SubscribeCancel from '@/components/User/Pages/PricingPages/subscribe-cancel.vue';

export default [
    { path: '/user/pricing', name: 'Pricing', component: Pricing, meta: { requiresAuth: true } },
    { path: '/subscribe/success', name: 'SubscribeSuccess', component: SubscribeSuccess, meta: { requiresAuth: true } },
    { path: '/subscribe/cancel', name: 'SubscribeCancel', component: SubscribeCancel, meta: { requiresAuth: true } },
]