import NotFound from '@/components/not-found.vue'
import authRouters from './authRouters'
import portalRouters from './portalRouters'
import adminRouters from './adminRouters'
import pricingRouters from './pricingRouters'
import Swal from "sweetalert2";
import { createRouter, createWebHistory } from "vue-router";
import { useRiskEvaluationActions } from '@/composables/riskEvaluation/useRiskEvaluationActions';
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';

const routes = [
  { path: '/:pathMatch(.*)*', name: 'NotFound', component: NotFound },
  ...authRouters,
  ...portalRouters,
  ...adminRouters,
  ...pricingRouters
];

const router = createRouter({
    history: createWebHistory(),
    routes
});

router.beforeEach(async (to, from, next) => {
  const isAdminRoute = to.fullPath.startsWith('/admin');

  const isUserAuthenticated = localStorage.getItem('JwtToken') && localStorage.getItem('UserInfo');
  const isAdminAuthenticated = localStorage.getItem('AdminJwtToken') && localStorage.getItem('AdminInfo');

  // Handle base path redirection
  if (to.path === '/' || to.path === '/admin') {
    if (isAdminRoute) {
      return next(isAdminAuthenticated ? { name: 'AdminDashboard' } : { name: 'AdminLogin' });
    } else {
      return next(isUserAuthenticated ? { name: 'Dashboard' } : { name: 'Login' });
    }
  }

  // Protect routes based on meta.requiresAuth and route type
  if (to.meta.requiresAuth) {
    if (isAdminRoute && !isAdminAuthenticated) {
      localStorage.setItem('AdminIntendedRoute', to.fullPath);
      return next({ name: 'AdminLogin' });
    } else if (!isAdminRoute && !isUserAuthenticated) {
      localStorage.setItem('UserIntendedRoute', to.fullPath);
      return next({ name: 'Login' });
    }
  }

  // Handle evaluation validity checks with separated logic
if (to.meta.requiresEvaluationValidity && isUserAuthenticated) {
  const evaluationType = localStorage.getItem('risk_analysis');
  
  try {
    if (evaluationType === 'risk') {
      const { countRemainingRiskEvaluations } = useRiskEvaluationActions();
      const remainingRiskEvaluations = await countRemainingRiskEvaluations();

      if (remainingRiskEvaluations <= 0) {
        await Swal.fire({
          icon: 'warning',
          title: 'No Risk Evaluation Left',
          text: 'You have no risk evaluations remaining. Please purchase a package.',
          confirmButtonText: 'Go to Pricing',
        });
        return next({ name: 'Pricing' });
      }
    } else if (evaluationType == 'fair') {
      const { countRemainingFairnessAnalysisEvaluations } = useFairnessAnalysisActions();
      const remainingFairnessAnalysisEvaluations = await countRemainingFairnessAnalysisEvaluations();

      if (remainingFairnessAnalysisEvaluations <= 0) {
        await Swal.fire({
          icon: 'warning',
          title: 'No Fairness Analysis Left',
          text: 'You have no fairness analysis remaining. Please purchase a package.',
          confirmButtonText: 'Go to Pricing',
        });
        return next({ name: 'Pricing' });
      }
    }
  } catch (error) {
    console.error('Evaluation validity check failed:', error);
    return next(false);
  }
}

  next();
});

export default router;