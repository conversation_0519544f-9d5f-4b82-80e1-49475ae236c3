<template>
    <div class="w-100 bg-white p-3 shadow mb-4 d-flex justify-content-between align-items-center">
        <input
        type="text"
        class="form-control"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
        @keyup="$emit('onSearch')"
        :placeholder="placeholder"
        style="width: 300px"
        />
        <router-link class="btn btn-primary rounded-pill px-3 btn-sm" :to="createRoute"> New {{ itemName }} </router-link>  
    </div>
</template>
  
<script>
  export default {
    props: {
      modelValue: String,
      placeholder: String,
      createRoute: Object,
      itemName: String,
    },
    emits: ['update:modelValue', 'onSearch'],
  };
</script>
  