<template>
    <div class="container text-center py-5">
      <h2>Subscription Cancelled</h2>
      <p>Your payment was not completed. If this was a mistake, you can try subscribing again.</p>
      <button @click="goToPricing" class="btn btn-primary mt-3">Return to Pricing</button>
    </div>
  </template>
  
  <script setup>
  import { useRouter } from 'vue-router';
  
  const router = useRouter();
  
  const goToPricing = () => {
    router.push({ name: 'Pricing' });
  };
  </script>
  