<template>
    <div class="container-lg">
        <SearchHeader
            v-model="keyword"
            @search="searchAwareness"
            placeholder="Search Awareness"
            :create-route="{ name: 'AwarenessCreate' }"
            create-button-text="New Awareness"
        />
        <LoadingSpinner :loading="loading" />
        <div class="w-100">
            <div class="row">
                <CourseCard v-for="course in courses" :key="course._id" :course="course" @delete="deleteAwareness"/>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useAwarenessManagementActions } from '@/composables/awareness/useAwarenessManagementActions';
import SearchHeader from '@/components/General/SearchHeader.vue';
import LoadingSpinner from '@/components/General/LoadingSpinner.vue';
import CourseCard from './CourseCard.vue';

const {
    loading,
    courses,
    keyword,
    searchAwareness,
    getAllAwareness,
    deleteAwareness
} = useAwarenessManagementActions();

onMounted(() => {
    getAllAwareness();
});
</script>