<template>
  <div class="progress-container">
    <span class="progress-text"> {{ completedLessons }} / {{ totalLessons }} lessons completed </span>
    <div class="progress-bar"><div class="progress-fill" :style="{ width: `${percentage}%` }" :class="progressClass"></div></div>
  </div>
</template>

<script setup>
const props = defineProps({
  percentage: { type: Number, default: 0 },
  completedLessons: { type: Number, default: 0 },
  totalLessons: { type: Number, default: 0 },
  progressClass: { type: String, required: true }
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/Dashboard/CourseProgressBar.scss';
</style>
