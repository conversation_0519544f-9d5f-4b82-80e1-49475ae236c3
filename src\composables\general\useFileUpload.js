import { useAwarenessManagementActions } from '@/composables/awareness/useAwarenessManagementActions';

export const useFileUpload = () => {
  const { uploadFile } = useAwarenessManagementActions();

  const attachFile = async (event, targetObject, bannerField = 'banner', bannerFullPathField = 'banner_full_path', mediaType = 1) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      const { data } = await uploadFile(file, mediaType);
      targetObject[bannerField] = data.file_path;
      targetObject[bannerFullPathField] = data.full_file_path;
      return { success: true, data };
    } catch (error) {
      console.error('Error uploading file:', error);
      return { success: false, error };
    }
  };

  return {
    attachFile
  };
};
