import axios from 'axios';
import router from '@/routers';
import ApiRoutes from '@/ApiRoutes';

const tokenKeys = {
  user: 'JwtToken',
  admin: 'AdminJwtToken',
};

const infoKeys = {
  user: 'UserInfo',
  admin: 'AdminInfo',
};

const loginRoutes = {
    user: { name: 'Login' },
    admin: { name: 'AdminLogin' },
};

const apiKey = '_@@mot4ai-secu2023re@@_';

const refreshState = {
  user: {
    isRefreshing: false,
    subscribers: [],
  },
  admin: {
    isRefreshing: false,
    subscribers: [],
  },
};

function getRoleFromRequest(config) {
  return config.url.includes('/admin') ? 'admin' : 'user';
}

function getToken(role) {
  return localStorage.getItem(tokenKeys[role]);
}

function setToken(role, token) {
  localStorage.setItem(tokenKeys[role], token);
}

function clearSession(role) {
  localStorage.removeItem(tokenKeys[role]);
  localStorage.removeItem(infoKeys[role]);
  router.push(loginRoutes[role]);
}

function addSubscriber(role, callback) {
  refreshState[role].subscribers.push(callback);
}

function notifySubscribers(role, newToken) {
  refreshState[role].subscribers.forEach(cb => cb(newToken));
  refreshState[role].subscribers = [];
}

async function refreshToken(role) {
    const currentToken = getToken(role);
    const refreshRoute = role === 'admin' ? ApiRoutes.AdminTokenRefresh : ApiRoutes.UserTokenRefresh(role)
    const response = await axios.get(refreshRoute, {
        headers: {
            Authorization: `Bearer ${currentToken}`,
            'x-api-key': apiKey,
        },
        isRefresh: true,
    });
    const newToken = response.data.token;
    setToken(role, newToken);
    notifySubscribers(role, newToken);
    return newToken;
}

export function setupRefreshInterceptor() {
    axios.interceptors.request.use(config => {
        const role = getRoleFromRequest(config);
        const token = getToken(role);
        if (token) config.headers['Authorization'] = `Bearer ${token}`;
        config.headers['x-api-key'] = apiKey;
        return config;
    });

    axios.interceptors.response.use(
        res => res,
        async err => {
            const originalRequest = err.config;
            
            if (originalRequest.isRefresh && err.response?.status === 401) {
                return Promise.reject(err);
            }
    
            if (err.response?.status !== 401 || originalRequest._retry) {
                return Promise.reject(err);
            }
            const role = getRoleFromRequest(originalRequest);
            const state = refreshState[role];
            originalRequest._retry = true;
    
            if (state.isRefreshing) {
                return new Promise(resolve => {
                    addSubscriber(role, newToken => {
                        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
                        resolve(axios(originalRequest));
                    });
                });
            }
            state.isRefreshing = true;
    
            try {
                const newToken = await refreshToken(role);
                originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
                return axios(originalRequest);
            } catch (refreshErr) {
                clearSession(role);
                return Promise.reject(refreshErr);
            } finally {
                state.isRefreshing = false;
            }
        }
    );
}