<template>
    <div class="row">
        <div class="col-xl-8 offset-xl-2">
            <div class="w-100" v-if="risk_evaluation != null">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <a @click="goBack" class="btn btn-sm btn-outline-secondary"><i class="fa fa-fw fa-arrow-left"></i> <span class="d-sm-inline d-none">Back</span></a>
                        <h3 class="text-center m-0" v-if="risk_analysis === 'risk'"><strong>Risk Evaluation Process</strong></h3>
                        <h3 class="text-center m-0" v-if="risk_analysis === 'fair'"><strong>Fair Decision Analysis</strong></h3>
                        <a class="invisible"><i class="fa fa-fw fa-arrow-left"></i> <span class="d-sm-inline d-none">Back</span></a>
                    </div>
                    <div class="card-body py-4 px-5">

                        <div class="w-100" v-if="risk_analysis === 'risk'">
                            <h2 class="mb-4 text-center"><strong class="text-secondary">Please select one Domain</strong></h2>
                        </div>
                        <div class="w-100" v-if="risk_analysis === 'fair'">
                            <h2 class="mb-4 text-center" v-if="sub_sectors.length == 0"><strong class="text-secondary">Please select one Domain</strong></h2>
                        </div>


                        <div class="w-100 mt-3">
                            <div class="row" v-if="sub_sectors.length == 0">
                                <div v-for="(sector, index) in sectors" class="col-lg-6" :class="{'offset-lg-3': isOddSector(index)}">
                                    <div class="w-100 mb-3">
                                        <div class="w-100 btn py-4"
                                             :class="{
                                            'disable-element': sector.is_active == 0,
                                            'btn-outline-success':risk_evaluation.evaluation_sector !== sector.uid,
                                            'btn-success':risk_evaluation.evaluation_sector === sector.uid
                                        }" @click="addSector(sector)">
                                            <h5 class="m-0"><i class="fa fa-fw fa-2x fa-cubes"></i> <br> {{ sector.title }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" v-if="sub_sectors.length > 0">
                                <div v-for="(sub_sector, sub_index) in sub_sectors" class="col-lg-6" :class="{'offset-lg-3': isOddSubSector(sub_index)}">
                                    <div class="w-100 mb-3">
                                        <div class="w-100 btn py-4"
                                             :class="{
                                            'btn-outline-success':risk_evaluation.evaluation_sub_sector !== sub_sector.uid,
                                            'btn-success':risk_evaluation.evaluation_sub_sector === sub_sector.uid
                                        }" @click="addSubSector(sub_sector)">
                                            <h5 class="m-0"><i class="fa fa-fw fa-2x fa-cubes"></i> <br> {{ sub_sector.title }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="w-100 mt-5" v-if="risk_analysis === 'fair'">
                            <h2 class="mb-4 text-center" v-if="sub_sectors.length > 0">
                                <a @click="startFairAnalysis" class="btn btn-success rounded-pill px-4 py-3">Start Analysis <i class="fa fa-fw fa-arrow-right"></i></a>
                            </h2>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

</template>


<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { useRiskEvaluationActions } from '@/composables/riskEvaluation/useRiskEvaluationActions';

export default {
    data() {
        return {
            risk_evaluation: null,
            sub_sectors: [],
            sectors: [],
            riskEvaluationActions: null
        }
    },
    async created() {
        // Initialize risk evaluation actions only
        this.riskEvaluationActions = useRiskEvaluationActions();
        
        // Always set analysis type to risk for this component
        localStorage.setItem('risk_analysis', 'risk');
        
        // Load risk evaluation sectors only
        this.loadRiskSectors();
        
        // Load evaluation data from localStorage
        if (localStorage.getItem('risk_evaluation') != null) {
            this.risk_evaluation = JSON.parse(localStorage.getItem('risk_evaluation'));
        }
    },
    methods: {
        isOddSector(index){
            if(index > 0 && index % 2 === 0 && (index+1) === this.sectors.length){
                return true;
            } else {
                return false;
            }
        },
        
        isOddSubSector(index){
            if(index > 0 && index % 2 === 0 && (index+1) === this.sub_sectors.length){
                return true;
            } else {
                return false;
            }
        },
        
        loadRiskSectors() {
            // Always use risk evaluation sectors endpoint
            ApiService.POST(ApiRoutes.EvaluationSectors, {}, (res) => {
                if (parseInt(res.status) === 200) {
                    this.sectors = res.data
                }
            })
        },
        
        addSector: function (sector) {
            this.risk_evaluation.evaluation_sector = sector.uid;
            if(sector.sub_sectors.length > 0){
                this.sub_sectors = sector.sub_sectors;
            } else {
                this.submitForm()
            }
        },
        
        addSubSector: function (sector) {
            this.risk_evaluation.evaluation_sub_sector = sector.uid;
            this.submitForm()
        },
        
        submitForm: function () {
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
            
            // Always navigate to risk evaluation questions
            this.$router.push({name: 'EtQuestions'});
        },
        
        async checkRiskEligibility() {
            try {
                const remaining = await this.riskEvaluationActions.countRemainingRiskEvaluations();
                return remaining > 0;
            } catch (error) {
                console.error('Failed to check risk eligibility:', error);
                return false;
            }
        },
        
        getPageTitle() {
            return 'Select Risk Evaluation Domain';
        },
        
        getPageDescription() {
            return 'Choose the industry or domain for your risk evaluation to get relevant risk factors and assessment criteria.';
        }
    }
}
</script>