import { ref } from 'vue';
import { useRouter } from 'vue-router';
import AwarenessManagementService from '@/services/awareness/AwarenessManagementService';
import { createToaster } from "@meforma/vue-toaster";
import Swal from 'sweetalert2';

export const useAwarenessManagementActions = () => {
  const Toaster = createToaster({ position: 'top-right' });
  const router = useRouter();

  const loading = ref(false);
  const courses = ref([]);
  const keyword = ref('');
  let searchTimeout = null;
  const currentCourse = ref(null);
  const topics = ref([]);
  const currentLesson = ref(null);

  const withLoading = async (operation) => {
    loading.value = true;
    try {
      return await operation();
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const confirmDelete = (title, message) => {
    return Swal.fire({
      title: `Delete ${title}`,
      html: `<strong class='text-danger'>Are you sure?</strong> <br><br> <p class='alert alert-danger'>${message}</p>`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ff005a',
      cancelButtonColor: '#777777',
      confirmButtonText: 'Delete'
    });
  };

  const showSuccessAndRefresh = async (message, refreshFn) => {
    if (refreshFn) await refreshFn();
    if (message) Toaster.success(message);
  };

  const showDeleteSuccess = (itemType) => {
    Swal.fire({
      title: 'Deleted!',
      text: `${itemType} has been deleted successfully.`,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  };

  const searchAwareness = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      getAllAwareness(keyword.value);
    }, 500);
  };

  const getAllAwareness = async (searchKeyword = '') => {
    return withLoading(async () => {
      const { data } = await AwarenessManagementService.getAllAwareness(searchKeyword);
      courses.value = data;
    });
  };

  const getSingleAwareness = async (courseId) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessManagementService.getSingleAwareness(courseId);
      currentCourse.value = data;
      return { data, msg };
    });
  };

  const createAwareness = async (courseData) => {
    return withLoading(async () => {
      const { msg } = await AwarenessManagementService.createAwareness(courseData);
      Toaster.success(msg);
      router.push({ name: 'Awareness' });
    });
  };

  const updateAwareness = async (courseId, courseData) => {
    return withLoading(async () => {
      const { msg } = await AwarenessManagementService.updateAwareness(courseId, courseData);
      Toaster.success(msg);
      router.push({ name: 'Awareness' });
    });
  };

  const deleteAwareness = async (course) => {
    const result = await confirmDelete('Awareness', 'This action will remove all related information of this Awareness permanently.');

    if (result.isConfirmed) {
      return withLoading(async () => {
        const { msg } = await AwarenessManagementService.deleteAwareness(course._id);
        await getAllAwareness(keyword.value);
        Toaster.success(msg);
      });
    }
  };

  const uploadFile = async (file, mediaType = 1) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("media_type", mediaType);

    return withLoading(async () => {
      const { data, msg } = await AwarenessManagementService.uploadFile(formData);
      return { data, msg };
    });
  };

  const getCourseTopics = async (courseId) => {
    return withLoading(async () => {
      const { data } = await AwarenessManagementService.getCourseTopics(courseId);
      topics.value = data;
    });
  };

  const createCourseTopic = async (courseId, topicData) => {
    return withLoading(async () => {
      const { msg } = await AwarenessManagementService.createCourseTopic(courseId, topicData);
      await showSuccessAndRefresh(msg, () => getCourseTopics(courseId));
    });
  };

  const updateCourseTopic = async (courseId, topicId, topicData) => {
    return withLoading(async () => {
      const { msg } = await AwarenessManagementService.updateCourseTopic(courseId, topicId, topicData);
      await showSuccessAndRefresh(msg, () => getCourseTopics(courseId));
    });
  };

  const deleteCourseTopic = async (courseId, topicId) => {
    const result = await confirmDelete('Topic', 'This action will remove all related lessons and questions permanently.');

    if (result.isConfirmed) {
      return withLoading(async () => {
        await AwarenessManagementService.deleteCourseTopic(courseId, topicId);
        await getCourseTopics(courseId);
        showDeleteSuccess('Topic');
      });
    }
  };

  const createLesson = async (courseId, topicId, lessonData) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessManagementService.createLesson(courseId, topicId, lessonData);
      await showSuccessAndRefresh(msg, () => getCourseTopics(courseId));
      return { data, msg };
    });
  };

  const updateLesson = async (courseId, topicId, lessonId, lessonData) => {
    return withLoading(async () => {
      const { msg } = await AwarenessManagementService.updateLesson(courseId, topicId, lessonId, lessonData);
      await showSuccessAndRefresh(msg, () => getCourseTopics(courseId));
    });
  };

  const getLesson = async (courseId, topicId, lessonId) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessManagementService.getLesson(courseId, topicId, lessonId);
      currentLesson.value = data;
      return { data, msg };
    });
  };

  const deleteLesson = async (courseId, topicId, lesson) => {
    const result = await confirmDelete('Lesson', 'This action will remove all related questions permanently.');

    if (result.isConfirmed) {
      return withLoading(async () => {
        await AwarenessManagementService.deleteLesson(courseId, topicId, lesson._id);
        await getCourseTopics(courseId);
        showDeleteSuccess('Lesson');
      });
    }
  };

  const createOrUpdateQuestion = async (courseId, topicId, lessonId, questionData, questionIndex) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessManagementService.createOrUpdateQuestion(courseId, topicId, lessonId, questionData);
      if (currentLesson.value && currentLesson.value.questions) {
        currentLesson.value.questions[questionIndex] = data;
      }
      Toaster.success(msg);
      return { data, msg };
    });
  };

  const deleteQuestion = async (courseId, topicId, lessonId, questionId) => {
    const result = await confirmDelete('Question', 'Are you sure you want to delete this question?');

    if (result.isConfirmed) {
      return withLoading(async () => {
        const { msg } = await AwarenessManagementService.deleteQuestion(courseId, topicId, lessonId, questionId);
        Toaster.success(msg);
      });
    }
  };

  const addNewQuestion = () => {
    if (!currentLesson.value) return;
    if (!currentLesson.value.questions) currentLesson.value.questions = [];
    const newQuestion = { _id: null, question: '', option_1: '', option_2: '', option_3: '', option_4: '', answer: '' };
    currentLesson.value.questions.push(newQuestion);
    return {
      questionIndex: currentLesson.value.questions.length - 1,
      question: newQuestion
    };
  };

  return {
    loading,
    courses,
    currentCourse,
    topics,
    currentLesson,
    keyword,
    searchAwareness,
    getAllAwareness,
    getSingleAwareness,
    createAwareness,
    updateAwareness,
    deleteAwareness,
    uploadFile,
    getCourseTopics,
    createCourseTopic,
    updateCourseTopic,
    deleteCourseTopic,
    createLesson,
    updateLesson,
    getLesson,
    deleteLesson,
    createOrUpdateQuestion,
    deleteQuestion,
    addNewQuestion
  };
};
