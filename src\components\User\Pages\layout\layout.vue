<!-- src/components/User/Pages/layout/layout.vue -->
<template>
    <div class="modern-app-layout">
        <!-- Top Navigation Bar -->
        <nav class="top-navbar">
            <div class="navbar-container">
                <!-- Menu Toggle & Logo -->
                <div class="navbar-left">
                    <button 
                        class="sidebar-toggle" 
                        @click="toggleSidebar"
                        :class="{ 'active': sidebarOpen }"
                    >
                        <span class="toggle-line"></span>
                        <span class="toggle-line"></span>
                        <span class="toggle-line"></span>
                    </button>
                    
                    <div class="navbar-brand">
                        <a href="https://raidot.ai" target="_blank" rel="noopener noreferrer" class="brand-link">
                            <span class="brand-text">Rai<span class="brand-accent">DOT</span></span>
                        </a>
                    </div>
                </div>

                <!-- Enhanced Search Bar -->
                <div class="navbar-search" v-if="showSearch && !isMobile">
                    <div class="search-container">
                        <i class="fa fa-search search-icon"></i>
                        <input 
                            type="text" 
                            placeholder="Search evaluations, reports, courses..."
                            class="search-input"
                            v-model="searchQuery"
                            @input="handleSearchInput"
                            @focus="showSearchResults = true"
                            @keydown.enter="performSearch"
                            @keydown.escape="clearSearch"
                            @keydown.arrow-down="navigateSearchResults(1)"
                            @keydown.arrow-up="navigateSearchResults(-1)"
                            ref="searchInput"
                        >
                        <button v-if="searchQuery || isSearching" @click="clearSearch" class="clear-search">
                            <i class="fa" :class="isSearching ? 'fa-spinner fa-spin' : 'fa-times'"></i>
                        </button>
                        
                        <!-- Search Results Dropdown -->
                            <div 
                                class="search-results" 
                                :class="{ 'expanded': showAllSearchResults, 'show': showSearchResults && (searchResults.total > 0 || searchQuery.length > 0 || isSearching) }"
                                v-if="showSearchResults && (searchResults.total > 0 || searchQuery.length > 0 || isSearching)"
                                @click.stop
                            >
                            <!-- Loading State -->
                            <div v-if="isSearching" class="search-loading">
                                <i class="fa fa-spinner fa-spin"></i>
                                <span>Searching...</span>
                            </div>
                            
                            <!-- No Results -->
                            <div v-else-if="searchQuery && searchResults.total === 0 && !isSearching" class="search-no-results">
                                <i class="fa fa-search"></i>
                                <span>No results found for "{{ searchQuery }}"</span>
                            </div>
                            
                            <!-- Search Results -->
                            <div v-else-if="searchResults.total > 0" class="search-results-list">
                                <!-- Evaluations Section -->
                                <div class="search-category" v-if="searchResults.evaluations && searchResults.evaluations.length > 0">
                                    <div class="category-header">
                                        <i class="fa fa-shield"></i>
                                        <span>Evaluations ({{ searchResults.evaluations.length }})</span>
                                    </div>
                                    <div 
                                        v-for="(item, index) in getDisplayedResults(searchResults.evaluations, 'evaluations')" 
                                        :key="`eval-${item._id || index}`"
                                        class="search-result-item"
                                        :class="{ 'highlighted': selectedSearchIndex === index }"
                                        @click="navigateToEvaluation(item)"
                                    >
                                        <!-- existing result item content -->
                                        <div class="result-icon">
                                            <i class="fa" :class="getEvaluationIcon(item.category)"></i>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">{{ item.project?.name || item.title || 'Untitled Evaluation' }}</div>
                                            <div class="result-subtitle">
                                                {{ getEvaluationType(item.category) }} • 
                                                {{ formatDate(item.created_at) }}
                                                <span v-if="item.risk_level" class="risk-level">• {{ item.risk_level }} Risk</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Courses Section -->
                                <div class="search-category" v-if="searchResults.courses && searchResults.courses.length > 0">
                                    <div class="category-header">
                                        <i class="fa fa-graduation-cap"></i>
                                        <span>Courses ({{ searchResults.courses.length }})</span>
                                    </div>
                                    <div 
                                        v-for="(item, index) in getDisplayedResults(searchResults.courses, 'courses')" 
                                        :key="`course-${item._id || index}`"
                                        class="search-result-item"
                                        @click="navigateToCourse(item)"
                                    >
                                        <!-- existing result item content -->
                                        <div class="result-icon">
                                            <i class="fa fa-book"></i>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">{{ item.title || 'Untitled Course' }}</div>
                                            <div class="result-subtitle">
                                                Course • {{ item.type || 'General' }}
                                                <span v-if="item.certificate">• Certificate Available</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>                                
                                <!-- Show More Results -->
                                <div v-if="searchResults.total > searchResultsLimit && !showAllSearchResults" class="search-more">
                                    <button @click="showMoreResults" class="view-all-btn">
                                        Show {{ Math.min(6, searchResults.total - searchResultsLimit) }} more results
                                    </button>
                                </div>
                                <!-- Show Less Button (when all results are shown) -->
                                <div v-if="showAllSearchResults && searchResults.total > 6" class="search-more">
                                    <button @click="showLessResults" class="view-all-btn show-less">
                                        Show less results
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Recent Searches -->
                            <div v-else-if="!searchQuery && recentSearches.length > 0" class="recent-searches">
                                <div class="category-header">
                                    <i class="fa fa-clock-o"></i>
                                    <span>Recent Searches</span>
                                </div>
                                <div 
                                    v-for="(search, index) in recentSearches.slice(0, 5)" 
                                    :key="`recent-${index}`"
                                    class="search-result-item recent-search"
                                    @click="setSearchQuery(search)"
                                >
                                    <div class="result-icon">
                                        <i class="fa fa-history"></i>
                                    </div>
                                    <div class="result-content">
                                        <div class="result-title">{{ search }}</div>
                                    </div>
                                    <button @click.stop="removeRecentSearch(search)" class="remove-recent">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Empty state when no query and no recent searches -->
                            <div v-else-if="!searchQuery && recentSearches.length === 0" class="search-empty">
                                <i class="fa fa-search"></i>
                                <span>Start typing to search evaluations and courses...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - User Info -->
                <div class="navbar-right">
                    <!-- Subscription Badge -->
                    <div class="subscription-badge" v-if="UserInfo">
                        <span 
                            class="badge" 
                            :class="Subscription.package_price !== 0 ? 'badge-premium' : 'badge-free'"
                        >
                            <i class="fa fa-star" v-if="Subscription.package_price !== 0"></i>
                            {{ Subscription.package_name }}
                        </span>
                    </div>

                    <!-- Enhanced Notifications -->
                    <div class="notifications" ref="notificationsRef">
                        <button 
                            class="notification-btn" 
                            @click="toggleNotifications"
                            :class="{ 'active': showNotifications }"
                        >
                            <i class="fa fa-bell"></i>
                            <span 
                                class="notification-count" 
                                v-if="unreadNotificationsCount > 0"
                                :class="{ 'pulse': hasNewNotifications }"
                            >
                                {{ unreadNotificationsCount > 99 ? '99+' : unreadNotificationsCount }}
                            </span>
                        </button>
                        
                        <!-- Notifications Dropdown -->
                        <div class="notifications-dropdown" :class="{ 'show': showNotifications }">
                            <div class="notifications-header">
                                <h3 class="notifications-title">Notifications</h3>
                                <div class="notifications-actions">
                                    <button 
                                        v-if="unreadNotificationsCount > 0" 
                                        @click="markAllAsRead"
                                        class="mark-all-read"
                                    >
                                        Mark all read
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Loading State -->
                            <div v-if="loadingNotifications" class="notifications-loading">
                                <i class="fa fa-spinner fa-spin"></i>
                                <span>Loading notifications...</span>
                            </div>
                            
                            <!-- Notifications List -->
                            <div v-else-if="notifications.length > 0" class="notifications-list">
                                <div 
                                    v-for="notification in displayedNotifications" 
                                    :key="notification._id"
                                    class="notification-item"
                                    :class="{ 'unread': !notification.read_at }"
                                    @click="handleNotificationClick(notification)"
                                >
                                    <div class="notification-icon" :class="getNotificationIconClass(notification.type)">
                                        <i class="fa" :class="getNotificationIcon(notification.type)"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">{{ notification.title }}</div>
                                        <div class="notification-message">{{ notification.message }}</div>
                                        <div class="notification-time">{{ formatNotificationTime(notification.created_at) }}</div>
                                    </div>
                                    <div class="notification-actions">
                                        <button 
                                            v-if="!notification.read_at"
                                            @click.stop="markAsRead(notification._id)"
                                            class="mark-read-btn"
                                            title="Mark as read"
                                        >
                                            <i class="fa fa-check"></i>
                                        </button>
                                        <button 
                                            @click.stop="deleteNotification(notification._id)"
                                            class="delete-btn"
                                            title="Delete notification"
                                        >
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Show More Notifications -->
                                <div v-if="notifications.length > notificationsLimit && !showAllNotifications" class="notifications-more">
                                    <button @click.stop="showMoreNotifications" class="view-all-btn">
                                        Show {{ Math.min(5, notifications.length - notificationsLimit) }} more notifications
                                    </button>
                                </div>
                                <!-- Show Less Button (when all notifications are shown) -->
                                <div v-if="showAllNotifications && notifications.length > notificationsLimit" class="notifications-more">
                                    <button @click.stop="showLessNotifications" class="view-all-btn show-less">
                                        Show less notifications
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Empty State -->
                            <div v-else class="notifications-empty">
                                <i class="fa fa-bell-slash"></i>
                                <span>No notifications yet</span>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="user-menu" ref="userMenu">
                        <button class="user-trigger" @click="toggleUserMenu">
                            <div class="user-avatar">
                                <img :src="getUserAvatar()" :alt="UserInfo?.name || 'User'">
                                <div class="online-status"></div>
                            </div>
                            <div class="user-info" v-if="UserInfo && !isMobile">
                                <span class="user-name">{{ UserInfo.name }}</span>
                                <i class="fa fa-chevron-down dropdown-icon"></i>
                            </div>
                        </button>

                        <!-- User Dropdown -->
                        <div class="user-dropdown" :class="{ 'show': showUserMenu }">
                            <div class="dropdown-header">
                                <div class="dropdown-avatar">
                                    <img :src="getUserAvatar()" :alt="UserInfo?.name || 'User'">
                                </div>
                                <div class="dropdown-info">
                                    <div class="dropdown-name">{{ UserInfo?.name || 'User' }}</div>
                                    <div class="dropdown-email">{{ UserInfo?.email || '<EMAIL>' }}</div>
                                </div>
                            </div>
                            
                            <div class="dropdown-divider"></div>
                            
                            <router-link :to="{ name: 'Profile' }" class="dropdown-item" @click="closeUserMenu">
                                <i class="fa fa-user"></i>
                                Profile Settings
                            </router-link>
                            
                            <router-link :to="{ name: 'Pricing' }" class="dropdown-item" @click="closeUserMenu">
                                <i class="fa fa-star"></i>
                                {{ Subscription.package_price !== 0 ? 'Manage Plan' : 'Upgrade Plan' }}
                                <span v-if="Subscription.package_price === 0" class="upgrade-tag">New</span>
                            </router-link>
                            
                            <div class="dropdown-divider"></div>
                            
                            <button @click="logout" class="dropdown-item logout-btn">
                                <i class="fa fa-sign-out"></i>
                                Sign Out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside class="sidebar" :class="{ 'open': sidebarOpen, 'collapsed': sidebarCollapsed }">
            <div class="sidebar-content">
                <!-- Sidebar Header -->
                <div class="sidebar-header">
                    <div class="sidebar-user" v-if="UserInfo && !sidebarCollapsed">
                        <div class="sidebar-avatar">
                            <img :src="getUserAvatar()" :alt="UserInfo.name">
                        </div>
                        <div class="sidebar-user-info">
                            <div class="sidebar-user-name">{{ UserInfo.name }}</div>
                            <div class="sidebar-user-role">
                                {{ Subscription.package_price !== 0 ? 'Premium User' : 'Free User' }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Collapse Toggle -->
                    <button 
                        class="sidebar-collapse-btn" 
                        @click="toggleSidebarCollapse"
                        v-if="!isMobile && sidebarOpen"
                        :title="sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
                    >
                        <i class="fa fa-angle-left" :class="{ 'rotated': sidebarCollapsed }"></i>
                    </button>
                </div>

                <!-- Navigation Menu -->
                <nav class="sidebar-nav">
                    <div class="nav-section">
                        <div class="nav-section-title" v-if="!sidebarCollapsed">Main Menu</div>
                        
                        <router-link 
                            v-for="item in mainNavItems" 
                            :key="item.name"
                            :to="{ name: item.route }" 
                            class="nav-item"
                            :class="{ 'active': isActiveRoute(item.route) }"
                            @click="closeSidebarOnMobile"
                            :title="sidebarCollapsed ? item.name : ''"
                        >
                            <div class="nav-item-icon">
                                <i :class="item.icon"></i>
                            </div>
                            <span class="nav-item-text" v-if="!sidebarCollapsed">{{ item.name }}</span>
                            <div class="nav-item-indicator"></div>
                        </router-link>
                    </div>

                    <div class="nav-section" v-if="quickActions.length > 0">
                        <div class="nav-section-title" v-if="!sidebarCollapsed">Quick Actions</div>
                        
                        <router-link 
                            v-for="action in quickActions" 
                            :key="action.name"
                            :to="{ name: action.route }" 
                            class="nav-item quick-action"
                            @click="closeSidebarOnMobile(action)"
                            :title="sidebarCollapsed ? action.name : ''"
                        >
                            <div class="nav-item-icon" :class="action.iconClass">
                                <i :class="action.icon"></i>
                            </div>
                            <span class="nav-item-text" v-if="!sidebarCollapsed">{{ action.name }}</span>
                        </router-link>
                    </div>
                <!-- Sidebar Footer -->
                <div class="sidebar-footer" v-if="!sidebarCollapsed">
                    <div class="footer-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ userStats.evaluations }}</div>
                            <div class="stat-label">Evaluations</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ userStats.reports }}</div>
                            <div class="stat-label">Reports</div>
                        </div>
                    </div>
                    
                    <div class="upgrade-prompt" v-if="Subscription.package_price === 0">
                        <div class="prompt-content">
                            <i class="fa fa-star prompt-icon"></i>
                            <div class="prompt-text">
                                <div class="prompt-title">Upgrade Your Plan</div>
                                <div class="prompt-subtitle">Unlock all features</div>
                            </div>
                        </div>
                        <router-link :to="{ name: 'Pricing' }" class="upgrade-btn">
                            Upgrade
                        </router-link>
                    </div>
                </div>
                </nav>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content" :class="{ 'sidebar-open': sidebarOpen && !isMobile, 'sidebar-collapsed': sidebarCollapsed && !isMobile }">
            <!-- Subscription Alert -->
            <div class="subscription-alert" v-if="Subscription.package_price === 0">
                <div class="alert-content">
                    <i class="fa fa-star alert-icon"></i>
                    <span class="alert-text">
                        <strong>Upgrade Your Plan</strong> and explore all features to evaluate Risks and analyse fair decisions.
                    </span>
                    <router-link :to="{ name: 'Pricing' }" class="alert-upgrade-btn">
                        Upgrade Plan
                    </router-link>
                </div>
            </div>

            <!-- Page Content -->
            <div class="content-container">
                <router-view />
            </div>
        </main>

        <!-- Mobile Overlay -->
        <div 
            class="mobile-overlay" 
            :class="{ 'show': sidebarOpen && isMobile }"
            @click="closeSidebar"
        ></div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { debounce } from 'lodash';

export default {
    name: 'ModernLayoutWithSidebar',
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            Subscription: JSON.parse(localStorage.getItem('Subscription')),
            sidebarOpen: false,
            sidebarCollapsed: false,
            showUserMenu: false,
            showSearch: true,
            searchQuery: '',
            showSearchResults: false,
            searchResultsLimit: 6,
            showAllSearchResults: false,
            isSearching: false,
            searchResults: { 
                evaluations: [], 
                courses: [], 
                total: 0 
            }, // Initialize with proper structure
            selectedSearchIndex: -1,
            recentSearches: (() => {
                try {
                    const saved = JSON.parse(localStorage.getItem('recentSearches') || '[]');
                    return Array.isArray(saved) ? saved : [];
                } catch (error) {
                    console.error('Error loading recent searches:', error);
                    return [];
                }
            })(),
            
            // Notifications
            showNotifications: false,
            notifications: [],
            loadingNotifications: false,
            unreadNotificationsCount: 0,
            hasNewNotifications: false,
            notificationPolling: null,
            notificationsLimit: 5,
            showAllNotifications: false,

            
            isMobile: false,
            mainNavItems: [
                { name: 'Dashboard', route: 'Dashboard', icon: 'fa fa-tachometer' },
                { name: 'Risk Evaluation', route: 'RiskEvaluation', icon: 'fa fa-shield' },
                { name: 'Fairness Analysis', route: 'FairDecisionAnalysis', icon: 'fa fa-balance-scale' },
                { name: 'Awareness', route: 'AwarenessEvaluation', icon: 'fa fa-graduation-cap' }
            ],
            quickActions: [
                { 
                    name: 'New Risk Evaluation', 
                    route: 'StartRiskEvaluation', 
                    icon: 'fa fa-plus',
                    iconClass: 'action-risk'
                },
                { 
                    name: 'New Fairness Analysis', 
                    route: 'StartFairDecisionAnalysis', 
                    icon: 'fa fa-plus',
                    iconClass: 'action-fairness'
                }
            ],
            userStats: {
                evaluations: 0,
                reports: 0
            }
        }
    },
    computed: {
        displayedNotifications() {
            if (!this.notifications || this.notifications.length === 0) return [];
            
            if (this.showAllNotifications) {
                return this.notifications;
            }
            
            return this.notifications.slice(0, this.notificationsLimit);
        },

        // Debounced search function
        debouncedSearch() {
            return debounce(() => {
                console.log('Debounced search triggered');
                this.performSearch();
            }, 300);
        }
    },
    methods: {
        // Sidebar methods
        toggleSidebar() {
            this.sidebarOpen = !this.sidebarOpen;
            if (this.isMobile) {
                this.sidebarCollapsed = false;
            }
        },
        
        closeSidebar() {
            this.sidebarOpen = false;
        },
        
        toggleSidebarCollapse() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
        },
        
        closeSidebarOnMobile(action) {
            if (action.route === 'StartRiskEvaluation') {
                localStorage.setItem('risk_analysis', 'risk');
            } else if (action.route === 'StartFairDecisionAnalysis') {
                localStorage.setItem('risk_analysis', 'fair');
            }
            if (this.isMobile) {
                this.sidebarOpen = false;
            }
        },
        
        toggleUserMenu() {
            this.showUserMenu = !this.showUserMenu;
            this.showNotifications = false;
        },
        
        closeUserMenu() {
            this.showUserMenu = false;
        },
        
        // Search methods
        handleSearchInput() {
            console.log('Search input changed:', this.searchQuery);
            
            if (this.searchQuery.trim()) {
                // Clear previous results immediately to show loading state
                this.searchResults = { evaluations: [], courses: [], total: 0 };
                this.showSearchResults = true;
                this.debouncedSearch();
            } else {
                this.searchResults = { evaluations: [], courses: [], total: 0 };
                this.showSearchResults = true;
            }
        },
        
        showMoreResults() {
            this.showAllSearchResults = true;
            // Scroll to bottom of search results to show new items
            this.$nextTick(() => {
                const searchResults = document.querySelector('.search-results');
                if (searchResults) {
                    searchResults.scrollTop = searchResults.scrollHeight;
                }
            });
        },
        
        showLessResults() {
            this.showAllSearchResults = false;
            // Scroll back to top
            this.$nextTick(() => {
                const searchResults = document.querySelector('.search-results');
                if (searchResults) {
                    searchResults.scrollTop = 0;
                }
            });
        },
        
        getDisplayedResults(items, type) {
            if (!items || items.length === 0) return [];
            
            if (this.showAllSearchResults) {
                return items; // Show all results
            }
            
            // Show limited results (3 each by default)
            const limit = type === 'evaluations' ? 3 : 3;
            return items.slice(0, limit);
        },
        
        showMoreNotifications() {
            this.showAllNotifications = true;
            // Scroll to bottom of notifications to show new items
            this.$nextTick(() => {
                const notificationsList = document.querySelector('.notifications-list');
                if (notificationsList) {
                    notificationsList.scrollTop = notificationsList.scrollHeight;
                }
            });
        },
        
        showLessNotifications() {
            this.showAllNotifications = false;
            // Scroll back to top
            this.$nextTick(() => {
                const notificationsList = document.querySelector('.notifications-list');
                if (notificationsList) {
                    notificationsList.scrollTop = 0;
                }
            });
        },
        
        async performSearch() {
                if (!this.searchQuery.trim()) {
                    this.searchResults = { evaluations: [], courses: [], total: 0 };
                    return;
                }
                
                this.isSearching = true;
                this.showSearchResults = true;
                this.showAllSearchResults = false; // Add this line to reset state
            
            try {
                // Call your search API endpoint
                const response = await new Promise((resolve) => {
                    ApiService.POST(ApiRoutes.Search || '/api/search', { 
                        query: this.searchQuery.trim(),
                        types: ['evaluations', 'courses'],
                        limit: 10
                    }, resolve);
                });
                
                    console.log('Search API response:', response);
                    
                    if (response.status === 200 && response.data) {
                        // Make sure we set the search results properly
                        this.searchResults = {
                            evaluations: response.data.evaluations || [],
                            courses: response.data.courses || [],
                            total: response.data.total || 0
                        };
                        
                        console.log('Processed search results:', this.searchResults);
                        
                        this.addToRecentSearches(this.searchQuery.trim());
                    } else {
                        console.error('Search failed:', response);
                        this.searchResults = { evaluations: [], courses: [], total: 0 };
                    }
            } catch (error) {
                console.error('Search error:', error);
                this.searchResults = { evaluations: [], courses: [], total: 0 };
            } finally {
                this.isSearching = false;
            }
        },
        //load search suggestions:
        async loadSearchSuggestions() {
            try {
                const response = await new Promise((resolve) => {
                      ApiService.GET(ApiRoutes.SearchSuggestions || '/api/search/suggestions', resolve);
                });
                
                console.log('Search suggestions response:', response);

                
                if (response.status === 200 && response.data) {
                    // Handle the response data properly
                    const suggestions = Array.isArray(response.data) ? response.data : [];
                    this.recentSearches = [...new Set([...this.recentSearches, ...suggestions])].slice(0, 10);
                }
            } catch (error) {
                console.error('Error loading search suggestions:', error);
            }
        },

        clearSearch() {
            this.searchQuery = '';
            this.searchResults = {};
            this.showSearchResults = false;
            this.selectedSearchIndex = -1;
            this.$refs.searchInput?.blur();
        },
        
        closeSearchResults() {
            this.showSearchResults = false;
            this.selectedSearchIndex = -1;
        },
        
        navigateSearchResults(direction) {
            const totalResults = (this.searchResults.evaluations?.length || 0) + 
                               (this.searchResults.courses?.length || 0);
            
            if (totalResults === 0) return;
            
            this.selectedSearchIndex += direction;
            
            if (this.selectedSearchIndex < 0) {
                this.selectedSearchIndex = totalResults - 1;
            } else if (this.selectedSearchIndex >= totalResults) {
                this.selectedSearchIndex = 0;
            }
        },
        
        setSearchQuery(query) {
            this.searchQuery = query;
            this.performSearch();
        },
        
        addToRecentSearches(query) {
            try {
                const searches = Array.isArray(this.recentSearches) ? this.recentSearches.filter(s => s !== query) : [];
                searches.unshift(query);
                this.recentSearches = searches.slice(0, 10);
                localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));
            } catch (error) {
                console.error('Error saving recent search:', error);
            }
        },
        
        removeRecentSearch(query) {
            this.recentSearches = this.recentSearches.filter(s => s !== query);
            localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));
        },
        
        navigateToEvaluation(evaluation) {
            const evaluationId = evaluation._id || evaluation.id;
            if (evaluationId) {
                this.$router.push({
                    name: 'EvaluationReport',
                    params: { evaluation_id: evaluationId }
                });
                this.closeSearchResults();
            }
        },
        
        navigateToCourse(course) {
            const courseId = course._id || course.id;
            if (courseId) {
                this.$router.push({
                    name: 'AwarenessCoursePreview',
                    params: { course_id: courseId }
                });
                this.closeSearchResults();
            }
        },
        
        viewAllSearchResults() {
            // Navigate to a dedicated search results page
            this.$router.push({
                name: 'SearchResults',
                query: { q: this.searchQuery }
            });
            this.closeSearchResults();
        },
        
        // Notification methods
        toggleNotifications() {
            this.showNotifications = !this.showNotifications;
            this.showUserMenu = false;
            
            if (this.showNotifications && !this.loadingNotifications) {
                this.loadNotifications();
            }
        },
        
        async loadNotifications() {
            this.loadingNotifications = true;
            try {
                const response = await new Promise((resolve) => {
                    ApiService.GET('/api/notifications', resolve);
                });
                
                if (response.status === 200) {
                    this.notifications = response.data.notifications || [];
                    this.unreadNotificationsCount = response.data.unread_count || 0;
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
            } finally {
                this.loadingNotifications = false;
            }
        },
        
        async markAsRead(notificationId) {
            try {
                const response = await new Promise((resolve) => {
                    ApiService.POST('/api/notifications/mark-read', { 
                        notification_id: notificationId 
                    }, resolve);
                });
                
                if (response.status === 200) {
                    const notification = this.notifications.find(n => n._id === notificationId);
                    if (notification) {
                        notification.read_at = new Date().toISOString();
                        this.unreadNotificationsCount = Math.max(0, this.unreadNotificationsCount - 1);
                    }
                }
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        },
        
        async markAllAsRead() {
            try {
                const response = await new Promise((resolve) => {
                    ApiService.POST('/api/notifications/mark-all-read', {}, resolve);
                });
                
                if (response.status === 200) {
                    this.notifications.forEach(n => {
                        if (!n.read_at) {
                            n.read_at = new Date().toISOString();
                        }
                    });
                    this.unreadNotificationsCount = 0;
                }
            } catch (error) {
                console.error('Error marking all notifications as read:', error);
            }
        },
        
        async deleteNotification(notificationId) {
            try {
                const response = await new Promise((resolve) => {
                    ApiService.DELETE(`/api/notifications/${notificationId}`, resolve);
                });
                
                if (response.status === 200) {
                    const index = this.notifications.findIndex(n => n._id === notificationId);
                    if (index > -1) {
                        const notification = this.notifications[index];
                        if (!notification.read_at) {
                            this.unreadNotificationsCount = Math.max(0, this.unreadNotificationsCount - 1);
                        }
                        this.notifications.splice(index, 1);
                    }
                }
            } catch (error) {
                console.error('Error deleting notification:', error);
            }
        },
        
        handleNotificationClick(notification) {
            // Mark as read if not already read
            if (!notification.read_at) {
                this.markAsRead(notification._id);
            }
            
            // Navigate based on notification type
            if (notification.action_url) {
                this.$router.push(notification.action_url);
            }
            
            this.showNotifications = false;
        },
        
        viewAllNotifications() {
            // Navigate to a dedicated notifications page
            this.$router.push({ name: 'Notifications' });
            this.showNotifications = false;
        },
        
        startNotificationPolling() {
            // Poll for new notifications every 30 seconds
            this.notificationPolling = setInterval(() => {
                this.checkForNewNotifications();
            }, 30000);
        },
        
        async checkForNewNotifications() {
            try {
                const response = await new Promise((resolve) => {
                    ApiService.GET('/api/notifications/count', resolve);
                });
                
                if (response.status === 200) {
                    const newCount = response.data.unread_count || 0;
                    if (newCount > this.unreadNotificationsCount) {
                        this.hasNewNotifications = true;
                        setTimeout(() => {
                            this.hasNewNotifications = false;
                        }, 3000);
                    }
                    this.unreadNotificationsCount = newCount;
                }
            } catch (error) {
                console.error('Error checking notifications:', error);
            }
        },
        
        // Utility methods
        isActiveRoute(routeName) {
            return this.$route.name === routeName;
        },
        
        getUserAvatar() {
            const baseUrl = import.meta.env.VITE_API_URL;
            return this.UserInfo?.avatar 
                ? `${baseUrl}/storage/media/image/${this.UserInfo.avatar}`
                : '/img/user.png';
        },
        
        getEvaluationType(category) {
            const types = {
                'et': 'General Risk',
                'eta': 'Industry Risk',
                'nt': 'Non-Tech Risk',
                'fd': 'General Fairness',
                'eta-fd': 'Industry Fairness'
            };
            return types[category] || 'Evaluation';
        },
        
        getEvaluationIcon(category) {
            if (['et', 'eta', 'nt'].includes(category)) {
                return 'fa-shield';
            }
            return 'fa-balance-scale';
        },
        
        getNotificationIcon(type) {
            const icons = {
                'evaluation_completed': 'fa-check-circle',
                'evaluation_failed': 'fa-exclamation-triangle',
                'course_completed': 'fa-graduation-cap',
                'certificate_ready': 'fa-certificate',
                'subscription_expired': 'fa-exclamation-circle',
                'subscription_renewed': 'fa-refresh',
                'system_update': 'fa-info-circle',
                'security_alert': 'fa-shield',
                'default': 'fa-bell'
            };
            return icons[type] || icons.default;
        },
        
        getNotificationIconClass(type) {
            const classes = {
                'evaluation_completed': 'success',
                'evaluation_failed': 'danger',
                'course_completed': 'success',
                'certificate_ready': 'warning',
                'subscription_expired': 'danger',
                'subscription_renewed': 'success',
                'system_update': 'info',
                'security_alert': 'warning',
                'default': 'default'
            };
            return classes[type] || classes.default;
        },
        
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        },
        
        formatNotificationTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
            
            if (diffInHours < 1) {
                const diffInMinutes = Math.floor((now - date) / (1000 * 60));
                return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
            } else if (diffInHours < 24) {
                return `${diffInHours}h ago`;
            } else {
                const diffInDays = Math.floor(diffInHours / 24);
                return diffInDays === 1 ? '1 day ago' : `${diffInDays} days ago`;
            }
        },
        
        logout() {
            ApiService.GET(ApiRoutes.UserLogout, (res) => {
                if (res.status === 200) {
                    localStorage.clear();
                    this.$router.push({ name: 'Login' });
                }
            });
        },
        
        handleResize() {
            this.isMobile = window.innerWidth < 1024;
            if (this.isMobile) {
                this.sidebarOpen = false;
                this.sidebarCollapsed = false;
                this.showSearchResults = false;
            } else {
                this.sidebarOpen = true;
            }
        },
        
        handleClickOutside(event) {
            // Close user menu
            if (this.$refs.userMenu && !this.$refs.userMenu.contains(event.target)) {
                this.showUserMenu = false;
            }
            
            // Close notifications
            if (this.$refs.notificationsRef && !this.$refs.notificationsRef.contains(event.target)) {
                this.showNotifications = false;
            }
            
            // Close search results - Fixed logic
            if (this.showSearchResults) {
                const searchContainer = document.querySelector('.search-container');
                if (searchContainer && !searchContainer.contains(event.target)) {
                    this.closeSearchResults();
                }
            }
        },        

        loadUserStats() {
            ApiService.GET(ApiRoutes.UserEvaluations, (res) => {
                if (res.status === 200) {
                    this.userStats.evaluations = res.data.length;
                    this.userStats.reports = res.data.filter(e => e.status === 'completed').length;
                }
            });
        }
    },
    mounted() {
        this.handleResize();
        window.addEventListener('resize', this.handleResize);
        document.addEventListener('click', this.handleClickOutside);
        this.loadUserStats();
        this.loadNotifications();
        this.startNotificationPolling();
        
        this.searchResults = { evaluations: [], courses: [], total: 0 };

        // Load initial notification count
        this.checkForNewNotifications();
    },
    beforeUnmount() {
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('click', this.handleClickOutside);
        
        // Clear notification polling
        if (this.notificationPolling) {
            clearInterval(this.notificationPolling);
        }
    }
}
</script>

<style scoped>

/* Enhanced Search Styles */
.search-container {
    position: relative;
    width: 100%;
}

.search-results {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    max-height: 400px;
    overflow-y: auto;
    z-index: 9999; /* Increased z-index */
    pointer-events: auto; /* Ensure clicks work */
}

.search-loading,
.search-no-results {
    padding: 2rem;
    text-align: center;
    color: #64748b;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.search-loading i {
    font-size: 1.5rem;
    color: #7925c7;
}

.search-results-list {
    padding: 0.5rem 0;
}

.search-category {
    margin-bottom: 0.5rem;
}

.category-header {
    padding: 0.75rem 1rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #475569;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    position: relative;
    pointer-events: auto; 
    animation: fadeInUp 0.3s ease-out;
}
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
.search-results.expanded {
    max-height: 60vh;
}
.search-result-item:hover,
.search-result-item.highlighted {
    background: #f8fafc;
    border-left-color: #7925c7;
}

.result-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.result-content {
    flex: 1;
    min-width: 0;
}

.result-title {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.125rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-subtitle {
    font-size: 0.875rem;
    color: #64748b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recent-search {
    position: relative;
}

.remove-recent {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    opacity: 0;
    transition: all 0.2s ease;
}

.recent-search:hover .remove-recent {
    opacity: 1;
}

.remove-recent:hover {
    background: #fee2e2;
    color: #ef4444;
}

.search-more {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

.view-all-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    color: #7925c7;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.view-all-btn:hover {
    background: #7925c7;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(121, 37, 199, 0.2);
}

.view-all-btn.show-less {
    background: rgba(121, 37, 199, 0.1);
    border-color: #7925c7;
}

.view-all-btn.show-less:hover {
    background: #f3f4f6;
    color: #7925c7;
}

/* Enhanced Notifications Styles */
.notifications-dropdown {
    position: absolute;
    top: calc(100% + 0.75rem);
    right: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    width: 380px;
    max-height: 500px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s ease;
    z-index: 1002;
}

.notifications-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.notifications-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.mark-all-read {
    background: none;
    border: none;
    color: #7925c7;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.mark-all-read:hover {
    background: rgba(121, 37, 199, 0.1);
}

.notifications-loading,
.notifications-empty {
    padding: 2rem;
    text-align: center;
    color: #64748b;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.notifications-loading i {
    font-size: 1.5rem;
    color: #7925c7;
}

.notifications-empty i {
    font-size: 2rem;
    color: #cbd5e1;
}

.notifications-list {
    max-height: 350px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.notification-item.unread {
    background: rgba(121, 37, 199, 0.05);
    border-left-color: #7925c7;
}

.notification-item:hover {
    background: #f8fafc;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.notification-icon.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification-icon.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification-icon.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.notification-icon.info {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.notification-icon.default {
    background: linear-gradient(135deg, #64748b, #475569);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.notification-message {
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-time {
    color: #9ca3af;
    font-size: 0.75rem;
}

.notification-actions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.mark-read-btn,
.delete-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.mark-read-btn {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.mark-read-btn:hover {
    background: #059669;
    color: white;
}

.delete-btn {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.delete-btn:hover {
    background: #dc2626;
    color: white;
}

.notifications-more {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

/* Notification Count Animation */
.notification-count.pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        background: #ef4444;
    }
    50% {
        transform: scale(1.1);
        background: #dc2626;
    }
}

/* Responsive Styles for Search and Notifications */
@media (max-width: 768px) {
    .notifications-dropdown {
        width: 320px;
        right: -1rem;
    }
    
    .search-results {
        left: -1rem;
        right: -1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

@media (max-width: 480px) {
    .notifications-dropdown {
        width: 280px;
        right: -2rem;
    }
    
    .notification-item {
        padding: 0.875rem 1rem;
    }
    
    .notification-icon {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }
}

/* Scrollbar Styling for Search and Notifications */
.search-results::-webkit-scrollbar,
.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.search-results::-webkit-scrollbar-track,
.notifications-list::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb,
.notifications-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover,
.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.modern-app-layout {
    min-height: 100vh;
    background: #f8fafc;
    display: flex;
    flex-direction: column;
}

/* Top Navigation */
.top-navbar {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 3px solid #7925c7;
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    height: 70px;
    max-width: none;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.15);
}

.toggle-line {
    width: 18px;
    height: 2px;
    background: white;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.sidebar-toggle.active .toggle-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.sidebar-toggle.active .toggle-line:nth-child(2) {
    opacity: 0;
}

.sidebar-toggle.active .toggle-line:nth-child(3) {
    transform: rotate(-45deg) translate(5px, -5px);
}
.navbar-brand .brand-link {
    text-decoration: none;
    color: white;
}

.brand-text {
    font-size: 1.75rem;
    font-weight: 800;
    color: white;
    letter-spacing: -0.5px;
}

.brand-accent {
    color: #ef4444;
}

/* Search Bar */
.navbar-search {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
}

.search-container {
    position: relative;
    width: 100%;
    z-index: 9998;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: white;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    border-color: #7925c7;
    box-shadow: 0 0 0 3px rgba(121, 37, 199, 0.2);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
}

.clear-search {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
}

.clear-search:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Navbar Right */
.navbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.subscription-badge .badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.badge-premium {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.badge-free {
    background: #64748b;
    color: white;
}

.notifications {
    position: relative;
}

.notification-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.notification-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}

.notification-count {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ef4444;
    color: white;
    font-size: 0.625rem;
    font-weight: 700;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* User Menu */
.user-menu {
    position: relative;
}

.user-trigger {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.375rem;
    padding-right: 1rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.user-trigger:hover {
    background: rgba(255, 255, 255, 0.15);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.online-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-icon {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.user-trigger:hover .dropdown-icon {
    transform: rotate(180deg);
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    min-width: 240px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

.dropdown-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.dropdown-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-name {
    font-weight: 600;
    color: #1f2937;
}

.dropdown-email {
    font-size: 0.875rem;
    color: #6b7280;
}

.dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.dropdown-item:hover {
    background: #f9fafb;
    color: #7925c7;
}

.upgrade-tag {
    margin-left: auto;
    background: #7925c7;
    color: white;
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
    border-radius: 8px;
    font-weight: 600;
}

.logout-btn:hover {
    background: #fef2f2;
    color: #dc2626;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 70px;
    left: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: white;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    transform: translateX(-100%);
    transition: all 0.3s ease;
    z-index: 999;
    border-right: 1px solid #e5e7eb;
}

.sidebar.open {
    transform: translateX(0);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1.5rem 0 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    flex-shrink: 0;
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 16px;
    border: 1px solid #e5e7eb;
}

.sidebar-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #7925c7;
}

.sidebar-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sidebar-user-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
}

.sidebar-user-role {
    font-size: 0.75rem;
    color: #6b7280;
}

.sidebar-collapse-btn {
    position: absolute;
    top: 80%;
    right: -12px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-collapse-btn:hover {
    background: #7925c7;
    color: white;
    border-color: #7925c7;
}

.sidebar-collapse-btn i.rotated {
    transform: rotate(180deg);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: 0 1rem;
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 1rem;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    padding: 0 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    color: #6b7280;
    text-decoration: none;
    border-radius: 12px;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    transition: width 0.3s ease;
    z-index: 0;
}

.nav-item:hover::before,
.nav-item.active::before {
    width: 4px;
}

.nav-item:hover {
    background: #f8fafc;
    color: #374151;
}

.nav-item.active {
    background: linear-gradient(135deg, rgba(121, 37, 199, 0.1), rgba(168, 85, 247, 0.1));
    color: #7925c7;
    font-weight: 600;
}

.nav-item-icon {
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.nav-item-text {
    flex: 1;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.nav-item-indicator {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #7925c7;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-item.active .nav-item-indicator {
    opacity: 1;
}

.quick-action .nav-item-icon.action-risk {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-radius: 8px;
    width: 28px;
    height: 28px;
}

.quick-action .nav-item-icon.action-fairness {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 8px;
    width: 28px;
    height: 28px;
}

/* Sidebar Footer */
.sidebar-footer {
    /*padding: 0 1.5rem 1.5rem 1.5rem;*/
    flex-shrink: 0;
}

.footer-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.stat-item {
    flex: 1;
    text-align: center;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
}

.stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.upgrade-prompt {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    border-radius: 16px;
    padding: 1rem;
    color: white;
    text-align: center;
}

.prompt-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.prompt-icon {
    font-size: 1.5rem;
    color: #fbbf24;
}

.prompt-title {
    font-weight: 600;
    font-size: 0.875rem;
}

.prompt-subtitle {
    font-size: 0.75rem;
    opacity: 0.9;
}

.upgrade-btn {
    background: white;
    color: #7925c7;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.upgrade-btn:hover {
    background: #f8fafc;
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    flex: 1;
    transition: all 0.3s ease;
    margin-left: 0;
    padding: 2rem;
}

.main-content.sidebar-open {
    margin-left: 280px;
}

.main-content.sidebar-collapsed {
    margin-left: 80px;
}

.content-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Subscription Alert */
.subscription-alert {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 16px;
    margin-bottom: 2rem;
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.alert-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1rem 2rem;
    color: white;
}

.alert-icon {
    font-size: 1.2rem;
    color: #fbbf24;
}

.alert-text {
    flex: 1;
    text-align: center;
    font-size: 0.95rem;
}

.alert-upgrade-btn {
    background: white;
    color: #1d4ed8;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-upgrade-btn:hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    height: calc(100vh - 70px);
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 998;
}

.mobile-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content.sidebar-open,
    .main-content.sidebar-collapsed {
        margin-left: 0;
    }
    
    .navbar-search {
        display: none;
    }
    
    .subscription-badge {
        display: none;
    }
    
    .user-info {
        display: none;
    }
    
    .sidebar {
        width: 280px;
    }
    
    .sidebar.collapsed {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .navbar-container {
        padding: 0 1rem;
    }
    
    .brand-text {
        font-size: 1.5rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .alert-content {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
        padding: 1rem;
    }
    
    .alert-text {
        font-size: 0.875rem;
    }
    
    .sidebar-header {
        padding: 1rem 1rem 0 1rem;
    }
    
    .sidebar-nav {
        padding: 0 0.75rem;
    }
    
    .sidebar-footer {
        padding: 0 1rem 1rem 1rem;
    }
}

@media (max-width: 480px) {
    .notifications {
        display: none;
    }
    
    .navbar-right {
        gap: 0.5rem;
    }
    
    .user-trigger {
        padding: 0.25rem;
        padding-right: 0.5rem;
    }
    
    .user-avatar {
        width: 28px;
        height: 28px;
    }
}

/* Enhanced Animations */
.nav-item {
    position: relative;
    overflow: hidden;
}

.nav-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.nav-item:hover::after {
    left: 100%;
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
    margin: 0.5rem 0;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Firefox scrollbar styling */
.sidebar-nav {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f8fafc;
}

/* Focus States for Accessibility */
.nav-item:focus,
.sidebar-toggle:focus,
.user-trigger:focus,
.dropdown-item:focus {
    outline: 2px solid #7925c7;
    outline-offset: 2px;
}

/* Loading States */
.nav-item.loading {
    pointer-events: none;
    opacity: 0.6;
}

.nav-item.loading .nav-item-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .modern-app-layout {
        background: #0f172a;
    }
    
    .sidebar {
        background: #1e293b;
        border-right-color: #334155;
    }
    
    .sidebar-user {
        background: linear-gradient(135deg, #1e293b, #334155);
        border-color: #475569;
    }
    
    .sidebar-user-name {
        color: #f1f5f9;
    }
    
    .nav-item {
        color: #94a3b8;
    }
    
    .nav-item:hover {
        background: #334155;
        color: #f1f5f9;
    }
    
    .nav-section-title {
        color: #64748b;
    }
    
    .footer-stats {
        background: #334155;
        border-color: #475569;
    }
    
    .stat-number {
        color: #f1f5f9;
    }
}

/* Print Styles */
@media print {
    .top-navbar,
    .sidebar,
    .subscription-alert {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
}
</style>