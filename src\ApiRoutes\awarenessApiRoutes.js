const BASE_URL = import.meta.env.VITE_API_URL;
const ApiAwarenessUrl = `${BASE_URL}/api/portal/awareness`;

const AwarenessApiRoutes = {
    AwarenessEvaluations: ApiAwarenessUrl + "/evaluations",
    GetCourses: ApiAwarenessUrl + "/get_courses",
    GetSingleCourse: (course_id) => `${ApiAwarenessUrl}/single/${course_id}`,
    CourseCertificate: (course_id) => `${ApiAwarenessUrl}/single/${course_id}/certificate`,
    GetTopicList: (course_id) => `${ApiAwarenessUrl}/${course_id}/topic/list`,
    GetSingleLesson: (course_id, topic_id, lesson_id) => `${ApiAwarenessUrl}/${course_id}/topic/${topic_id}/lesson/single/${lesson_id}`,
    CompleteLesson: (course_id, topic_id, lesson_id) => `${ApiAwarenessUrl}/${course_id}/topic/${topic_id}/lesson/single/${lesson_id}/complete`,
    SubmitQuiz: (course_id, topic_id, lesson_id) => `${ApiAwarenessUrl}/${course_id}/topic/${topic_id}/lesson/single/${lesson_id}/quiz/submit`,
};

export default AwarenessApiRoutes;