<template>
    <button :class="computedClass" :disabled="loading" @click="onClick">
      <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
      <span v-if="!loading">{{ defaultText }}</span>
      <span v-else>{{ loadingText }}</span>
    </button>
</template>
  
<script setup>
  import { computed } from 'vue' 
  const props = defineProps({
    loading: Boolean,
    defaultText: String,
    loadingText: {
      type: String,
      default: 'Processing...'
    },
    onClick: Function,
    class: {
      type: String,
      default: 'w-100 rounded-pill btn btn-lg btn-outline-success'
    }
  })
  
  const computedClass = computed(() => {
    return props.class
  })
</script>
  