<template>
    <div class="modern-course-container">
        <div class="course-layout" v-if="!loading && course != null">
            <UserCourseBreadcrumb :courseTitle="course.title" />
            
            <div class="course-main-content">
                <UserCourseSidebar
                    :topics="topics"
                    :overallProgress="getOverallProgress(topics)"
                    :activeTopic="topic_id"
                    :currentLesson="lesson_id"
                    :courseId="course._id"
                    :getTopicProgress="getTopicProgress"
                    @toggle-topic="toggleTopic"
                />

                <main class="course-content">
                    <div v-if="isOverviewPage" class="course-overview">
                        <div class="overview-card">
                            <div class="overview-header">
                                <div class="course-info">
                                    <h2 class="overview-title">{{ course.title }}</h2>
                                    <p class="overview-description">{{ course.description }}</p>
                                    <UserCourseOverviewStats :topicsCount="topics.length" :lessonsCount="getTotalLessons(topics)" :completedCount="getCompletedLessons(topics)"/>
                                </div>
                            </div>
                            
                            <UserCourseActionSection
                                :progress="getOverallProgress(topics)"
                                :completedCount="getCompletedLessons(topics)"
                                :totalCount="getTotalLessons(topics)"
                                :hasAvailableLessons="!!getFirstAvailableLesson(topics)"
                                @start-learning="() => startFirstLesson(course, topics)"
                                @continue-learning="() => continueFromLastLesson(course, topics)"
                                @download-certificate="() => downloadCourseCertificate(courseId)"
                            />
                        </div>
                    </div>
                    <div v-else class="lesson-content"> <router-view @refresh="restartPage" @currentLesson="getCurrentLesson"/> </div>
                </main>
            </div>
        </div>
        <UserLoadingState v-else-if="loading || course == null" :loading="true" message="Course Loading..." color="#059669" />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import UserLoadingState from '@/components/General/UserLoadingState.vue';
import UserCourseBreadcrumb from './UserCourseBreadcrumb.vue';
import UserCourseSidebar from './UserCourseSidebar.vue';
import UserCourseOverviewStats from './UserCourseOverviewStats.vue';
import UserCourseActionSection from './UserCourseActionSection.vue';

const route = useRoute();

const {
  loading,
  currentCourse: course,
  topics,
  getSingleCourse,
  getCourseTopics,
  getOverallProgress,
  getTotalLessons,
  getCompletedLessons,
  getTopicProgress,
  getFirstAvailableLesson,
  startFirstLesson,
  continueFromLastLesson,
  downloadCourseCertificate
} = useAwarenessActions();

const topic_id = ref(null);
const lesson_id = ref(null);
const courseId = computed(() => route.params.course_id);
const isOverviewPage = computed(() => route.name === 'AwarenessCoursePreview');

const getCurrentLesson = (payload) => {
  topic_id.value = payload.topic_id;
  lesson_id.value = payload.lesson_id;
};

const toggleTopic = (topicId) => {
  topic_id.value = topic_id.value === topicId ? null : topicId;
};

const loadCourseData = async () => {
  if (!courseId.value) return;

  try {
    await getSingleCourse(courseId.value);
    await getCourseTopics(courseId.value);
  } catch (error) {
    console.error('Error loading course data:', error);
  }
};

const restartPage = async (data) => {
  await loadCourseData();
  if (data?.topic_id && data?.lesson_id) getCurrentLesson(data);
};

onMounted(async () => {
  await loadCourseData();
  window.scrollTo(0, 0);
});

watch(courseId, async (newId, oldId) => {
  if (newId !== oldId && newId) await loadCourseData();
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CoursePreview/course-preview.scss';
</style>
