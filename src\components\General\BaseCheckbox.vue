<template>
    <div :class="wrapperClass">
      <div class="form-check">
        <input
          class="form-check-input"
          type="checkbox"
          :checked="modelValue"
          @change="$emit('update:modelValue', $event.target.checked)"
          :disabled="disabled"
        >
        <label class="form-check-label">{{ label }}</label>
      </div>
    </div>
</template>
  
<script>
  export default {
    props: {
      modelValue: {
        type: Boolean,
        required: true
      },
      label: {
        type: String,
        default: ''
      },
      disabled: {
        type: Boolean,
        default: false
      },
      wrapperClass: {
        type: String,
        default: '',
      },
    }
  }
</script>
  