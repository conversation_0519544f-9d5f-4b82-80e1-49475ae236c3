<template>
    <div class="row">
        <div class="col-xl-8 offset-xl-2">
            <form class="w-100">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <p class="text-center m-0">We are almost done!</p>
                        <h3 class="text-center m-0">Fair Decision Analysis</h3>
                    </div>
                    <div class="card-body p-5">

                        <div class="w-100 text-center mb-5" v-if="loadingStep === 1">
                            <div class="alert alert-warning">
                                <strong>Processing Your Submitted Answers...</strong>
                                <div class="progress border border-warning rounded-pill mt-3">
                                    <div class="progress-bar progress-bar-striped bg-warning progress-bar-animated" :style="{width: progress+'%'}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="w-100 text-center mb-5" v-if="loadingStep === 2">
                            <div class="alert alert-info">
                                <strong>Analyzing Fairness For Your Sector...</strong>
                                <div class="progress border border-info rounded-pill mt-3">
                                    <div class="progress-bar progress-bar-striped bg-info progress-bar-animated" :style="{width: progress+'%'}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="w-100 text-center" v-if="loadingStep === 3">
                            <div class="alert alert-success">
                                <strong>Evaluating Bias and Fairness Metrics...</strong>
                                <div class="progress border border-success rounded-pill mt-3">
                                    <div class="progress-bar progress-bar-striped bg-success progress-bar-animated" :style="{width: progress+'%'}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="w-100 text-center" v-if="loadingStep === 4">
                            <div class="alert alert-success">
                                <strong>Generating Visual Fairness Analysis Report</strong>
                                <div class="progress border border-success rounded-pill mt-3">
                                    <div class="progress-bar progress-bar-striped bg-success progress-bar-animated" :style="{width: progress+'%'}"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </form>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';
import Swal from "sweetalert2";

export default {
    name: 'FairnessAlmostDone',
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            loadingStep: 1,
            progress: 0,
            evaluation_id: 0,
            risk_evaluation: null,
            fairnessAnalysisActions: null
        }
    },
    async created() {
        // Initialize fairness analysis actions
        this.fairnessAnalysisActions = useFairnessAnalysisActions();
        
        // Set analysis type to fairness
        localStorage.setItem('risk_analysis', 'fair');
        
        if (localStorage.getItem('risk_evaluation') == null) {
            this.$router.push({name: 'StartFairDecisionAnalysis'})
        } else {
            this.risk_evaluation = JSON.parse(localStorage.getItem('risk_evaluation'));
            
            // Validate that we have fairness answers
            if (this.validateFairnessAnswers()) {
                this.submitFairnessAnalysis();
                this.showLoading(1);
            } else {
                this.$router.push({name: 'FairnessChoice'})
            }
        }
    },
    mounted() {
        window.scrollTo(0, 0);
    },
    methods: {
validateFairnessAnswers() {
    // Check if we have fairness analysis answers
    if (!this.risk_evaluation.answers) {
        console.error('No answers found in risk_evaluation');
        return false;
    }
    
    console.log('Risk evaluation object:', this.risk_evaluation);
    console.log('Answers object:', this.risk_evaluation.answers);
    
    // Check for general fairness answers (fd category)
    if (this.risk_evaluation.category === 'fd') {
        const hasFdAnswers = this.risk_evaluation.answers.fd && 
                           Object.keys(this.risk_evaluation.answers.fd).length > 0;
        console.log('FD answers check:', hasFdAnswers);
        return hasFdAnswers;
    }
    
    // Check for industry-specific fairness answers (eta-fd category)
    if (this.risk_evaluation.category === 'eta-fd') {
        // Look for domain-specific answers
        const domainKeys = Object.keys(this.risk_evaluation.answers).filter(key => 
            key.startsWith('domain_')
        );
        console.log('Domain keys found:', domainKeys);
        const hasValidDomainAnswers = domainKeys.length > 0 && 
                                    domainKeys.some(key => 
                                        this.risk_evaluation.answers[key] && 
                                        Object.keys(this.risk_evaluation.answers[key]).length > 0
                                    );
        console.log('Domain answers check:', hasValidDomainAnswers);
        return hasValidDomainAnswers;
    }
    
    console.error('Unknown category:', this.risk_evaluation.category);
    return false;
},
        
submitFairnessAnalysis: function () {
    let THIS = this;
    
    // Ensure we have the required data structure for backend
    console.log('Submitting fairness analysis with data:', this.risk_evaluation);
    
    // Make sure project data exists
    if (!this.risk_evaluation.project) {
        console.error('Missing project data');
        this.$router.push({name: 'StartFairDecisionAnalysis'});
        return;
    }
    
    // Ensure category is set correctly
    if (!this.risk_evaluation.category || !['fd', 'eta-fd'].includes(this.risk_evaluation.category)) {
        console.error('Invalid or missing category:', this.risk_evaluation.category);
        this.$router.push({name: 'FairnessChoice'});
        return;
    }
    
    localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
    
    // Submit to backend using existing endpoint
    ApiService.POST(ApiRoutes.RiskEvaluationSubmit, {
        risk_evaluation: this.risk_evaluation
    }, function (res) {
        console.log('Backend submission response:', res);
        if (res.status === 200) {
            localStorage.removeItem('risk_analysis');
            localStorage.removeItem('risk_evaluation');
            THIS.evaluation_id = res.data._id;
            THIS.evaluation_report();
        } else {
            console.error('Failed to submit fairness analysis:', res);
            // Handle error - could show message to user
            Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                text: 'Failed to submit your fairness analysis. Please try again.',
            });
        }
    })
},
        
        evaluation_report: function () {
    const THIS = this;
    ApiService.POST(ApiRoutes.UserEvaluationReport, {
        evaluation_id: THIS.evaluation_id
    }, function (res) {
        console.log('Report generation response:', res);
        if (res.status === 200) {
            THIS.$router.push({ 
                name: 'FairnessReport', 
                params: { evaluation_id: THIS.evaluation_id } 
            });
        } else {
            console.error('Failed to generate report:', res);
            // Fallback to general evaluation report
            THIS.$router.push({ 
                name: 'EvaluationReport', 
                params: { evaluation_id: THIS.evaluation_id } 
            });
        }
    })
},
        
        showLoading: function (step) {
            if (step <= 4) {
                this.progress = 0;
                this.loadingStep = step;
                setTimeout(() => {
                    this.progress = 30;
                    setTimeout(() => {
                        this.progress = 50;
                        setTimeout(() => {
                            this.progress = 75;
                            setTimeout(() => {
                                this.progress = 90;
                                setTimeout(() => {
                                    this.progress = 100;
                                    setTimeout(() => {
                                        this.showLoading(step + 1);
                                    }, 1000)
                                }, 1000)
                            }, 1000)
                        }, 1000)
                    }, 1000)
                }, 1000)
            }
        }
    }
}
</script>

<style scoped>
/* Fairness-specific styling */
.card-header h3 {
    color: #7c3aed;
}

.alert-info {
    background-color: #e0e7ff;
    border-color: #7c3aed;
    color: #5b21b6;
}

.bg-info {
    background-color: #7c3aed !important;
}

.border-info {
    border-color: #7c3aed !important;
}
</style>