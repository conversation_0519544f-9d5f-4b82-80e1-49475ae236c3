import { watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAwarenessManagementActions } from './useAwarenessManagementActions';
import { useAwarenessPageState } from './useAwarenessPageState';

export const useAwarenessPreview = () => {
  const route = useRoute();
  const {
    currentCourse: course,
    topics,
    getSingleAwareness,
    getCourseTopics,
    loading,
    createCourseTopic,
    updateCourseTopic,
    deleteCourseTopic,
    deleteLesson
  } = useAwarenessManagementActions();
  const currentAwarenessPage = useAwarenessPageState();

  const courseId = computed(() => currentAwarenessPage.courseId.value);

  const loadCourseData = async () => {
    if (!courseId.value) return;

    try {
      await getSingleAwareness(courseId.value);
      await refreshTopics();
    } catch (error) {
      console.error('Error loading course data:', error);
    }
  };

  const refreshTopics = async () => {
    try {
      await getCourseTopics(courseId.value);
      currentAwarenessPage.updatePageState();
    } catch (error) {
      console.error('Error refreshing topics:', error);
    }
  };

  watch(() => route.params.course_id, (newId, oldId) => {
    if (newId !== oldId) {
      loadCourseData();
    }
  });

  watch(() => route.fullPath, (newPath, oldPath) => {
    if (newPath !== oldPath) {
      currentAwarenessPage.updatePageState();
    }
  });

  return {
    course,
    topics,
    loading,
    currentAwarenessPage,
    showCourseOverview: currentAwarenessPage.showCourseOverview,
    loadCourseData,
    refreshTopics,
    createCourseTopic,
    updateCourseTopic,
    deleteCourseTopic,
    deleteLesson
  };
};
