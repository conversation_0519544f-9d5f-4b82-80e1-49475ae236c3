<template>
    <LessonForm :is-edit-mode="false" :course-id="courseId" @submit="handleCreateLesson"/>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useAwarenessManagementActions } from '@/composables/awareness/useAwarenessManagementActions';
import { useLessonRouteParams } from '@/composables/awareness/useLessonRouteParams';
import LessonForm from './LessonForm.vue';

const router = useRouter();
const { createLesson } = useAwarenessManagementActions();
const { courseId, topicId } = useLessonRouteParams();

const emit = defineEmits(['refresh']);

const handleCreateLesson = async (lessonData) => {
    const { data } = await createLesson(courseId.value, topicId.value, lessonData);
    emit('refresh');
    router.push({ name: 'AdminLessonPreview', params: { course_id: courseId.value, topic_id: topicId.value, lesson_id: data.lesson_id }});
};
</script>