.awareness-evaluation-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-container {
    padding: 2rem 1rem;
}

.dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* <PERSON><PERSON> Styles for Empty State */
.btn-primary.large {
    background: rgba(5, 150, 105, 0.2);
    border: 1px solid rgba(5, 150, 105, 0.3);
    color: #059669;
    padding: 1.125rem 2.5rem;
    font-size: 1.125rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-primary.large:hover {
    background: rgba(5, 150, 105, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: #047857;
    text-decoration: none;
}

.btn-secondary {
    background: white;
    color: #059669;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-secondary:hover {
    background: #f0fdf4;
    border-color: #059669;
    transform: translateY(-2px);
    color: #047857;
    text-decoration: none;
}

/* Courses Section */
.courses-section {
    margin-bottom: 3rem;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem 0.5rem;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0.5rem 0.25rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print styles */
@media print {
    .page-header {
        background: white !important;
        color: black !important;
    }
}
