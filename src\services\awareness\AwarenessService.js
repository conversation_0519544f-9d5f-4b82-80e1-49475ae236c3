import ApiService from '@/services/ApiService';
import ApiRoutes from '@/ApiRoutes';

const handleResponse = (res, resolve, reject, errorMessage) => {
    if (res.status === 200) {
        resolve({ data: res.data, msg: res.msg });
    } else {
        console.error(errorMessage, res.error);
        reject(new Error(res.error || errorMessage));
    }
};

const AwarenessService = {
    
    getAwarenessEvaluations() {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.AwarenessEvaluations, (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch awareness evaluations")
            );
        });
    },

    getAvailableCourses() {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetCourses, (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch available courses")
            );
        });
    },

    getSingleCourse(courseId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetSingleCourse(courseId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch course details")
            );
        });
    },

    getCourseTopics(courseId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetTopicList(courseId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch course topics")
            );
        });
    },

    getSingleLesson(courseId, topicId, lessonId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.GetSingleLesson(courseId, topicId, lessonId), (res) =>
                handleResponse(res, resolve, reject, "Failed to fetch lesson details")
            );
        });
    },

    completeLesson(courseId, topicId, lessonId) {
        return new Promise((resolve, reject) => {
            ApiService.GET(ApiRoutes.CompleteLesson(courseId, topicId, lessonId), (res) =>
                handleResponse(res, resolve, reject, "Failed to mark lesson as complete")
            );
        });
    },

    submitQuiz(courseId, topicId, lessonId, questions) {
        return new Promise((resolve, reject) => {
            ApiService.POST(ApiRoutes.SubmitQuiz(courseId, topicId, lessonId), { questions }, (res) =>
                handleResponse(res, resolve, reject, "Failed to submit quiz")
            );
        });
    },

    downloadCourseCertificate(courseId) {
        return new Promise((resolve, reject) => {
            ApiService.GETPDF(ApiRoutes.CourseCertificate(courseId), (response) => {
                resolve(response);
            }, (error) => {
                console.error("Failed to download certificate", error);
                reject(new Error("Failed to download certificate"));
            });
        });
    }
};

export default AwarenessService;
