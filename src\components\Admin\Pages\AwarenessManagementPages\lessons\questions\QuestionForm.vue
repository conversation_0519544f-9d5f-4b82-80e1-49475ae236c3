<template>
  <form class="w-100" @submit.prevent="handleSave">
    <div class="w-100 p-3 bg-light border">
      <div class="row">
        <div class="col-xl-12">
          <div class="form-group mb-3">
            <label class="form-label text-black-50"><strong>Question</strong></label>
            <textarea v-model="formData.question" name="question" class="form-control" placeholder="Write question here..." style="resize: vertical" required></textarea>
          </div>
        </div>
      </div>
      
      <div class="col-xl-12 mb-4">
        <div class="row">
          <div class="col-xl-6">
            <div class="form-group mb-3">
              <label class="form-label text-black-50"><strong>Option (A)</strong></label>
              <input name="option_1" v-model="formData.option_1" class="form-control" placeholder="Answer Option" required>
            </div>
          </div>
          <div class="col-xl-6">
            <div class="form-group mb-3">
              <label class="form-label text-black-50"><strong>Option (B)</strong></label>
              <input name="option_2" v-model="formData.option_2" class="form-control" placeholder="Answer Option" required>
            </div>
          </div>
          <div class="col-xl-6">
            <div class="form-group mb-3">
              <label class="form-label text-black-50"><strong>Option (C)</strong></label>
              <input name="option_3" v-model="formData.option_3" class="form-control" placeholder="Answer Option" required>
            </div>
          </div>
          <div class="col-xl-6">
            <div class="form-group mb-3">
              <label class="form-label text-black-50"><strong>Option (D)</strong></label>
              <input name="option_4" v-model="formData.option_4" class="form-control" placeholder="Answer Option" required>
            </div>
          </div>
          <div class="col-xl-12">
            <div class="form-group mb-3">
              <label class="form-label text-success"><strong>Correct Answer</strong></label>
              <select v-model="formData.answer" class="form-select" required>
                <option value="">-- Choose Correct Answer --</option>
                <option value="1">Option (A)</option>
                <option value="2">Option (B)</option>
                <option value="3">Option (C)</option>
                <option value="4">Option (D)</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-12">
        <div class="w-100 text-end">
          <button type="submit" class="btn btn-sm btn-success" :disabled="loading"> <i class="fa fa-fw fa-check"></i> </button>
          <a class="btn btn-sm btn-danger ms-2" href="javascript:void(0)" @click="handleDelete" ><i class="fa fa-fw fa-trash"></i></a>
        </div>
      </div>
    </div>
  </form>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  question: { type: Object, required: true }, 
  questionIndex: { type: Number, required: true }, 
  loading: { type: Boolean, default: false }
});

const emit = defineEmits(['save', 'delete']);

const formData = ref({ _id: null, question: '', option_1: '', option_2: '', option_3: '', option_4: '', answer: '' });

const handleSave = () => {
  emit('save', { question: formData.value, index: props.questionIndex });
};

const handleDelete = () => {
  emit('delete', { question: formData.value, index: props.questionIndex });
};

watch(() => props.question, (newQuestion) => {
  if (newQuestion) formData.value = { ...newQuestion };
}, { immediate: true, deep: true });
</script>
