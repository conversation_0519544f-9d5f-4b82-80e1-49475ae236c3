<template>
    <AwarenessForm :is-edit-mode="false" @submit="handleCreateCourse"/>
</template>

<script setup>
import { useAwarenessManagementActions } from '@/composables/awareness/useAwarenessManagementActions';
import AwarenessForm from './AwarenessForm.vue';

const { createAwareness } = useAwarenessManagementActions();

const handleCreateCourse = async (courseData) => {
    try {
        await createAwareness(courseData);
    } catch (error) {
        console.error('Error creating course:', error);
    }
};
</script>