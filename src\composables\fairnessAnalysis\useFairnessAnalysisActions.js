import { usePricingActions } from '@/composables/pricing/usePricingActions';
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export const useFairnessAnalysisActions = () => {
  const { getSubscription } = usePricingActions();

  const fetchCurrentPackage = async () => {
    const subscription = await getSubscription();
    return subscription.package;
  };

  const fetchFairnessEvaluations = async () => {
    return new Promise((resolve, reject) => {
      ApiService.GET(ApiRoutes.UserEvaluations, (res) => {
        if (res.status === 200) {
          const evaluations = res.data;
          const fairness = evaluations.filter(e => ['fd', 'eta-fd'].includes(e.category));
          resolve(fairness);
        } else {
          reject(new Error('Failed to fetch fairness evaluations'));
        }
      });
    });
  };

  const countRemainingFairnessAnalysisEvaluations = async () => {
    const currentPackage = await fetchCurrentPackage();
    const fairnessEvaluations = await fetchFairnessEvaluations();
    console.log(currentPackage.fair_decision_analysis.sessions - fairnessEvaluations.length);
    return currentPackage.fair_decision_analysis.sessions - fairnessEvaluations.length;
  };

  const getFairnessAnalysisPackageInfo = async () => {
    const currentPackage = await fetchCurrentPackage();
    return currentPackage.fair_decision_analysis;
  };

  const getFairnessAnalysisUsageStats = async () => {
    const currentPackage = await fetchCurrentPackage();
    const fairnessEvaluations = await fetchFairnessEvaluations();
    
    return {
      total: currentPackage.fair_decision_analysis.sessions,
      used: fairnessEvaluations.length,
      remaining: currentPackage.fair_decision_analysis.sessions - fairnessEvaluations.length,
      evaluations: fairnessEvaluations
    };
  };

  return {
    countRemainingFairnessAnalysisEvaluations,
    fetchFairnessEvaluations,
    fetchCurrentPackage,
    getFairnessAnalysisPackageInfo,
    getFairnessAnalysisUsageStats
  };
};