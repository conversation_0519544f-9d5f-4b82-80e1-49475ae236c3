import { usePricingActions } from '@/composables/pricing/usePricingActions';
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export const useRiskEvaluationActions = () => {
  const { getSubscription } = usePricingActions();

  const fetchCurrentPackage = async () => {
    const subscription = await getSubscription();
    return subscription.package;
  };

  const fetchRiskEvaluations = async () => {
    return new Promise((resolve, reject) => {
      ApiService.GET(ApiRoutes.UserEvaluations, (res) => {
        if (res.status === 200) {
          const evaluations = res.data;
          const risk = evaluations.filter(e =>['et', 'eta', 'nt'].includes(e.category));
          resolve(risk);
        } else {
          reject(new Error('Failed to fetch risk evaluations'));
        }
      });
    });
  };

  const countRemainingRiskEvaluations = async () => {
    const currentPackage = await fetchCurrentPackage();
    const riskEvaluations = await fetchRiskEvaluations();
    console.log(currentPackage.risk_evaluation.sessions - riskEvaluations.length);
    return currentPackage.risk_evaluation.sessions - riskEvaluations.length;
  };

  const getRiskEvaluationPackageInfo = async () => {
    const currentPackage = await fetchCurrentPackage();
    return currentPackage.risk_evaluation;
  };

  const getRiskEvaluationUsageStats = async () => {
    const currentPackage = await fetchCurrentPackage();
    const riskEvaluations = await fetchRiskEvaluations();
    
    return {
      total: currentPackage.risk_evaluation.sessions,
      used: riskEvaluations.length,
      remaining: currentPackage.risk_evaluation.sessions - riskEvaluations.length,
      evaluations: riskEvaluations
    };
  };

  return {
    countRemainingRiskEvaluations,
    fetchRiskEvaluations,
    fetchCurrentPackage,
    getRiskEvaluationPackageInfo,
    getRiskEvaluationUsageStats
  };
};