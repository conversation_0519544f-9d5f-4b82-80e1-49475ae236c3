<template>
  <div class="course-header">
    <div class="header-content">
      <div class="course-breadcrumb">
        <router-link :to="{ name: backRoute }" class="breadcrumb-link"> <i class="fa fa-arrow-left"></i> {{ backText }} </router-link>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-current">{{ courseTitle }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  courseTitle: { type: String, required: true },
  backRoute: { type: String, default: 'CourseList' },
  backText: { type: String, default: 'All Courses' }
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CoursePreview/UserCourseBreadcrumb.scss';
</style>
