<template>
    <div class="container-lg py-4">
      <PageLoader v-if="adminLoading" />
      <PricingForm v-else :pricing="pricing" :isEdit="true" :onSubmit="() => updatePricing(package_id,pricing)" />
    </div>
</template>
  
<script setup>
  import { onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { usePricingFormState } from '@/composables/pricing/usePricingFormState';
  import { usePricingActions } from '@/composables/pricing/usePricingActions';
  import PageLoader from '@/components/General/PageLoader.vue';
  import PricingForm from '@/components/Admin/Pages/PricingPages/PricingForm.vue';

  const route = useRoute();
  const { pricing } = usePricingFormState();
  const package_id = route.params.package_id;
  const { adminLoading, getSinglePricing, updatePricing } = usePricingActions();

  onMounted(async () => {
    const { data, msg } = await getSinglePricing(package_id);
    Object.assign(pricing, data);
  });
</script>