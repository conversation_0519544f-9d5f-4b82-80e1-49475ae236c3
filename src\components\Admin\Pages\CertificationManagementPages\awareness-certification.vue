<template>
    <div class="container-lg">
        <div class="w-100">
            <div class="row">
                <div class="col-xl-10 offset-xl-1 col-md-12">

                    <div class="w-100">
                        <div class="card question-holder">
                            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                                <h4 class="m-0"><strong class="text-secondary">Awareness Certificates</strong></h4>
                                <router-link :to="{name: 'AwarenessCertificationSettings'}" class="btn btn-sm btn-primary rounded-pill px-3">Certificate Settings</router-link>
                            </div>
                            <div class="card-body p-2 p-lg-3">
                                <div class="w-100" v-if="certificates.length === 0">
                                    <p class="alert alert-info">No awareness certificate to show.</p>
                                </div>
                                <div class="w-100" v-if="certificates.length > 0">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>Awareness</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th style="width: 150px" class="text-center">Date</th>
                                            <th style="width: 150px"></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="certificate in certificates">
                                            <td>{{certificate.awareness.title}}</td>
                                            <td>{{certificate.full_name}}</td>
                                            <td>{{certificate.email}}</td>
                                            <td class="text-center">{{certificate.created_at}}</td>
                                            <td class="text-center">
                                                <a target="_blank" class="btn btn-sm rounded-pill btn-success px-3" :href="certificate.file_path">Download</a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>


<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {createToaster} from "@meforma/vue-toaster";
const Toaster = createToaster({position: 'top-right'});

export default {
    components: {
        flatPickr
    },
    data() {
        return {
            Loading: false,
            certificates: [],
        }
    },
    methods: {
        getAwarenessCertificates() {
            this.Loading = true;
            ApiService.POST(ApiRoutes.AwarenessCertificates, {}, (res) => {
                if (parseInt(res.status) === 200) {
                    this.certificates = res.data;
                }
            })
        }
    },
    created() {
        this.getAwarenessCertificates()
    },
    mounted() {
    }
};
</script>
