<template>
    <div class="container-lg">
        <PageHeader v-model="keyword" :placeholder="'Search Pricing'" :createRoute="{ name: 'AdminPricingCreate' }" itemName="Pricing" @onSearch="debouncedSearch"/>
        <PageLoader v-if="adminLoading" />
        <div class="row">
            <PricingCard v-for="pricing_package in pricing_packages" :key="pricing_package._id" :pricing="pricing_package" :generateButtons="generateAdminPricingButtons" :userType="'admin'"/>
        </div>
    </div>
</template>
  
<script setup>
import { ref, onMounted } from 'vue';
import PageHeader from "@/components/General/PageHeader.vue";
import PageLoader from "@/components/General/PageLoader.vue";
import PricingCard from "@/components/General/PricingCard.vue";
import { usePricingActions } from '@/composables/pricing/usePricingActions';

const { adminLoading, pricing_packages, getAllPricing, generateAdminPricingButtons } = usePricingActions();
const keyword = ref('');

const debouncedSearch = () => {
  // TODO: Add debounce logic here
};

onMounted(() => {
  getAllPricing('admin');
});
</script>