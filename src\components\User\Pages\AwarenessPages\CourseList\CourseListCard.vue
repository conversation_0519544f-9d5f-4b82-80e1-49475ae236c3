<template>
  <div :class="['course-card', { 'list-view': viewMode === 'list' }]" @click="startCourse(course)" :style="{ animationDelay: `${animationDelay}s` }">
    <div class="course-image">
      <img v-if="course.banner_full_path" :src="course.banner_full_path" :alt="course.title" class="course-img" />
      <div v-else class="course-placeholder"><i class="fa fa-book"></i></div>
      
      <div class="course-overlay">
        <div class="course-level"><span class="level-badge">{{ course.level || 'Beginner' }}</span></div>
      </div>
    </div>

    <div class="course-content">
      <div class="course-meta">
        <span class="course-category">AI Awareness</span>
        <span class="course-duration">{{ course.duration || 'Self-paced' }}</span>
      </div>
      
      <h3 class="course-title">{{ course.title }}</h3>
      <p v-if="course.description" class="course-description">{{ course.description }}</p>
      
      <div class="course-features">
        <div class="feature"><i class="fa fa-play-circle"></i><span>Interactive Content</span></div>
        <div class="feature"><i class="fa fa-certificate"></i><span>Certificate Included</span></div>
        <div class="feature"><i class="fa fa-mobile"></i><span>Mobile Friendly</span></div>
      </div>
    </div>

    <div class="course-footer">
      <div class="course-info">
        <div class="info-item"><i class="fa fa-users"></i><span>{{ course.enrolled || Math.floor(Math.random() * 500) + 100 }}+ learners</span></div>
        <div class="info-item"><i class="fa fa-star"></i><span>{{ course.rating || '4.8' }} rating</span></div>
      </div>
      <router-link :to="{ name: 'AwarenessCoursePreview', params: { course_id: course._id } }" class="start-course-btn"><span>Start Course</span><i class="fa fa-arrow-right"></i></router-link>
    </div>

    <div class="card-shine"></div>
  </div>
</template>

<script setup>
const props = defineProps({
  course: { type: Object, required: true },
  viewMode: { type: String, required: true },
  animationDelay: { type: Number, default: 0 },
  startCourse: { type: Function, required: true }
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CourseList/CourseListCard.scss';
</style>
