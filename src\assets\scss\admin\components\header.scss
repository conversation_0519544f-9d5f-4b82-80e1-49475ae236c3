.header {
    position: relative;
    z-index: 1021;
    .<PERSON>t<PERSON><PERSON>{
        text-decoration: none;
        font-size: 30px;
        font-weight: bold;
        color: var(--mot-theme-color);
        border: 0!important;
        //text-shadow: 1px 1px 1px var(--mot-theme-color);
    }
    .navbar{
        .navbar-nav{
            .nav-item{
                .nav-link{
                    font-size: 14px;
                    color: #777777;
                    border-bottom: 1px solid transparent;
                    @include transition(all 0.2s linear);
                    &:active,&:hover{
                        border-bottom: 1px solid #777777;
                        @include transition(all 0.2s linear);
                    }
                    &.router-link-exact-active{
                        color: var(--mot-theme-color);
                        border-bottom: 1px solid var(--mot-theme-color);
                        @include transition(all 0.2s linear);
                    }
                }
            }
        }
    }
    .profile {
        float: right;
        display: table;

        .profile-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;

            .profile-info-details {
                margin-right: 15px;
            }

            .profile-avatar {
                height: 45px;
                width: 45px;
                border-radius: 50%;
                display: block;
                overflow: hidden;

                img {
                    width: 100%;
                }
            }
        }
    }
}
