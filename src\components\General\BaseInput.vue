<template>
    <div :class="wrapperClass">
      <div class="mb-3">
        <label v-if="label" class="form-label"><strong>{{ label }}</strong></label>
        <input
          :type="type"
          class="form-control"
          :placeholder="placeholder"
          :value="modelValue"
          @input="$emit('update:modelValue', castValue($event.target.value))"
          :disabled="disabled"
        />
      </div>
    </div>
</template>
  
<script>
  export default {
    props: {
      modelValue: {
        type: [String, Number],
        required: true,
      },
      type: {
        type: String,
        default: 'text', // can be 'text', 'number', etc.
      },
      label: {
        type: String,
        default: '',
      },
      placeholder: {
        type: String,
        default: '',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      wrapperClass: {
        type: String,
        default: '',
      },
    },
    methods: {
      castValue(value) {
        return this.type === 'number' ? Number(value) : value;
      }
    }
  };
</script>
  