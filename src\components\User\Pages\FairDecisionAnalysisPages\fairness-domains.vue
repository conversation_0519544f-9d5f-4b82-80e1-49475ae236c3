<template>
    <div class="row">
        <div class="col-xl-10 offset-xl-1">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <a @click="goBack" class="btn btn-sm btn-outline-secondary">
                        <i class="fa fa-fw fa-arrow-left"></i> 
                        <span class="d-sm-inline d-none">Back</span>
                    </a>
                    <h3 class="text-center m-0">
                        <strong>Select Fairness Analysis Domain</strong>
                    </h3>
                    <a class="invisible">
                        <i class="fa fa-fw fa-arrow-left"></i> 
                        <span class="d-sm-inline d-none">Back</span>
                    </a>
                </div>
                <div class="card-body py-3 px-4">
                    <div class="w-100 py-3">
                        <p class="text-center text-muted mb-4">
                            Choose the industry or domain for your fairness analysis to get targeted bias detection and mitigation recommendations.
                        </p>
                        
                        <!-- Sectors Display -->
                        <div class="w-100" v-if="sub_sectors.length === 0">
                            <div class="row">
                                <div class="col-xl-6 mb-3" 
                                     v-for="(sector, index) in sectors" 
                                     :key="sector._id"
                                     :class="{'col-xl-12': isOddSector(index)}">
                                    <div class="w-100 h-100 text-center p-4 border shadow sector-card fairness-sector" 
                                         @click="addSector(sector)"
                                         style="cursor: pointer; transition: all 0.3s ease;">
                                        <div class="sector-icon mb-3">
                                            <i class="fa fa-balance-scale fa-2x text-primary"></i>
                                        </div>
                                        <h5><strong>{{ sector.title }}</strong></h5>
                                        <p class="text-muted">{{ sector.description || 'Fairness analysis for this sector' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sub-sectors Display -->
                        <div class="w-100 mt-4" v-if="sub_sectors.length > 0">
                            <h4 class="text-center mb-4">
                                <strong>Select Specific Area</strong>
                            </h4>
                            <div class="row">
                                <div class="col-xl-6 mb-3" 
                                     v-for="(sector, index) in sub_sectors" 
                                     :key="sector._id"
                                     :class="{'col-xl-12': isOddSubSector(index)}">
                                    <div class="w-100 h-100 text-center p-4 border shadow sector-card fairness-sector" 
                                         @click="addSubSector(sector)"
                                         style="cursor: pointer; transition: all 0.3s ease;">
                                        <div class="sector-icon mb-3">
                                            <i class="fa fa-balance-scale fa-2x text-info"></i>
                                        </div>
                                        <h5><strong>{{ sector.title }}</strong></h5>
                                        <p class="text-muted">{{ sector.description || 'Specialized fairness analysis' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';

export default {
    data() {
        return {
            risk_evaluation: null,
            sub_sectors: [],
            sectors: [],
            fairnessAnalysisActions: null
        }
    },
    async created() {
        // Initialize fairness analysis actions
        this.fairnessAnalysisActions = useFairnessAnalysisActions();
        
        // Always set analysis type to fairness for this component
        localStorage.setItem('risk_analysis', 'fair');
        
        // Load evaluation data
        if (localStorage.getItem('risk_evaluation') == null) {
            this.$router.push({name: 'StartFairDecisionAnalysis'});
        } else {
            this.risk_evaluation = JSON.parse(localStorage.getItem('risk_evaluation'));
            // Set category for industry-specific fairness
            this.risk_evaluation.category = 'eta-fd';
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
        }
        
        // Load fairness sectors from backend
        this.loadFairnessSectors();
    },
    methods: {
        isOddSector(index){
            if(index > 0 && index % 2 === 0 && (index+1) === this.sectors.length){
                return true;
            } else {
                return false;
            }
        },
        
        isOddSubSector(index){
            if(index > 0 && index % 2 === 0 && (index+1) === this.sub_sectors.length){
                return true;
            } else {
                return false;
            }
        },
        
        loadFairnessSectors() {
            // Call backend API for fairness sectors
            ApiService.POST(ApiRoutes.FdSectors, {}, (res) => {
                if (parseInt(res.status) === 200) {
                    this.sectors = res.data;
                } else {
                    console.error('Failed to load fairness sectors:', res);
                }
            })
        },
        
        addSector: function (sector) {
            this.risk_evaluation.evaluation_sector = sector.uid;
            if(sector.sub_sectors && sector.sub_sectors.length > 0){
                this.sub_sectors = sector.sub_sectors;
            } else {
                this.submitForm()
            }
        },
        
        addSubSector: function (sector) {
            this.risk_evaluation.evaluation_sub_sector = sector.uid;
            this.submitForm()
        },
        
        submitForm: function () {
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
            
            // Navigate to industry-specific fairness questions
            this.$router.push({
                name: 'EtaFdQuestions', 
                params: { domain: this.risk_evaluation.evaluation_sector }
            });
        },
        
        goBack() {
            this.$router.push({name: 'FairnessChoice'})
        },
        
        async checkFairnessEligibility() {
            try {
                const remaining = await this.fairnessAnalysisActions.countRemainingFairnessAnalysisEvaluations();
                return remaining > 0;
            } catch (error) {
                console.error('Failed to check fairness eligibility:', error);
                return false;
            }
        },
        
        getPageTitle() {
            return 'Select Fairness Analysis Domain';
        },
        
        getPageDescription() {
            return 'Choose the industry or domain for your fairness analysis to get targeted bias detection and mitigation recommendations.';
        }
    }
}
</script>

<style scoped>
.fairness-sector {
    border-left: 4px solid #7c3aed;
}

.fairness-sector:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.15) !important;
    border-left-color: #5b21b6;
}

.sector-icon {
    transition: all 0.3s ease;
}

.fairness-sector:hover .sector-icon {
    transform: scale(1.1);
}

.sector-card {
    border-radius: 15px;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.text-primary {
    color: #7c3aed !important;
}

.text-info {
    color: #5b21b6 !important;
}
</style>