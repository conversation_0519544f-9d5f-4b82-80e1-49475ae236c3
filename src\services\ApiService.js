import axios from 'axios';

const getToken = (url) => {
    const path = new URL(url, window.location.origin).pathname;
    const tokenKey = path.startsWith('/admin/') ? 'AdminJwtToken' : 'JwtToken';
    return localStorage.getItem(tokenKey);
};

const getHeaders = (url) => {
    const token = getToken(url);
    return {
        'Content-Type': 'application/json; charset=utf-8',
        'x-api-key': '_@@mot4ai-secu2023re@@_',
        ...(token && { Authorization: `Bearer ${token}` })
    };
}

const getPdfHeaders = (url) => {
    const token = getToken(url);
    return {
        'x-api-key': '_@@mot4ai-secu2023re@@_',
        'Accept': 'application/pdf',
        ...(token && { Authorization: `Bearer ${token}` })
    };
}

const getMediaHeaders = (url) => {
    const token = getToken(url);
    return {
        'Content-Type': 'multipart/form-data',
        'x-api-key': '_@@mot4ai-secu2023re@@_',
        ...(token && { Authorization: `Bearer ${token}` })
    };
};
// Handle successful API responses
const responseHandler = (response, callback) => {
    if (response.status === 200) {
        callback(response.data);
    }
};

const blobResponseHandler = (response, callback) => {
    if (response.status === 200) {
        callback(response);
    }
};

// Handle API errors
const errorHandler = (err) => {
    const errorCode = parseInt(err.toString().replace(/\D/g, ""));
    if (errorCode === 401) {
        window.location.reload();
    }
};

// ApiService object with HTTP request methods
const ApiService = {
    // POST method with JSON data
    POST: (url, param, callback) => {
        axios.post(url, param, { headers: getHeaders(url) })
            .then((response) => responseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // POST method with FormData for file uploads
    POST_FORM_DATA: (url, formData, callback) => {
        axios.post(url, formData, { headers: getMediaHeaders(url) })
            .then((response) => responseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // PUT method with JSON data
    PUT: (url, data, callback) => {
        axios.put(url, data, { headers: getHeaders(url) })
            .then((response) => responseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // GET method
    GET: (url, callback) => {
        axios.get(url, { headers: getHeaders(url) })
            .then((response) => responseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // GET method for PDF
    GETPDF: (url, callback) => {
        axios.get(url, { headers: getPdfHeaders(url), responseType: 'blob' })
            .then((response) => blobResponseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // DELETE method
    DELETE: (url, callback) => {
        axios.delete(url, { headers: getHeaders(url) })
            .then((response) => responseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // POST method for file uploads (alias for POST_FORM_DATA)
    UPLOAD: (url, media, callback) => {
        axios.post(url, media, { headers: getMediaHeaders(url) })
            .then((response) => responseHandler(response, callback))
            .catch((err) => errorHandler(err));
    },

    // Custom error handler for form validation errors
    ErrorHandler(errors) {
        $('.is-invalid').removeClass('is-invalid');
        $('.error-report').html('');
        $('.error-report-g').html('');
        $.each(errors, (field, message) => {
            if (field === 'error') {
                $('.error-report-g').html('<p class="alert alert-danger">' + message + '</p>');
            } else {
                $('[name=' + field + ']').addClass('is-invalid');
                $('[name=' + field + ']').closest('.form-group').find('.error-report').html(message);
            }
        });
    },

    // Clear form validation errors
    ClearErrorHandler() {
        $('.is-invalid').removeClass('is-invalid');
        $('.error-report').html('');
    }
};

export default ApiService;