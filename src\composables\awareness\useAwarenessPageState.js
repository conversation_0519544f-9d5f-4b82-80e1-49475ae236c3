import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';

export const useAwarenessPageState = () => {
  const route = useRoute();
  
  const activeTopicId = ref(null);
  const activeLessonId = ref(null);
  const currentPage = ref('AwarenessPreview');

  const courseId = computed(() => route.params.course_id);
  const topicId = computed(() => route.params.topic_id);
  const lessonId = computed(() => route.params.lesson_id);

  const showCourseOverview = computed(() => {
    const lessonRelatedPages = [
      'AdminLessonPreview',
      'LessonCreate',
      'LessonEdit'
    ];
    return !lessonRelatedPages.includes(currentPage.value);
  });

  const setActiveLesson = (payload) => {
    activeTopicId.value = payload.topic_id;
    activeLessonId.value = payload.lesson_id;
  };

  const setActiveTopic = (topicId) => {
    activeTopicId.value = topicId;
  };

  const updatePageState = () => {
    currentPage.value = route.name;
    if (topicId.value) {
      activeTopicId.value = topicId.value;
    }
    if (lessonId.value) {
      activeLessonId.value = lessonId.value;
    }
  };

  return {
    activeTopicId,
    activeLessonId,
    currentPage,
    courseId,
    topicId,
    lessonId,
    showCourseOverview,
    setActiveLesson,
    setActiveTopic,
    updatePageState
  };
};
