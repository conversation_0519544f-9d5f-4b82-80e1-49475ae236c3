<template>
  <div class="w-100">
    <div class="col-lg-6 offset-lg-3">
      <div class="w-100 bg-white shadow-sm px-3 py-5 border rounded-3">
        <div class="w-100 text-center" v-if="course.banner_full_path">
          <img :src="course.banner_full_path" alt="Banner" class="img-fluid rounded-3 mb-2 bg-light p-2 shadow-sm border" style="max-height: 200px; max-width: 100%;">
        </div>
        <div class="w-100">
          <h1 class="text-center">{{ course.title }}</h1>
          <p class="text-center">{{ course.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  course: { type: Object, required: true}
});
</script>
