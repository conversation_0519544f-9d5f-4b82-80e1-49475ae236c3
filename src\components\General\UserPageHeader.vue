<template>
  <div class="user-page-header" :style="backgroundStyle">
    <div class="header-content">
      <div class="header-text">
        <h1 class="page-title"> <i :class="icon + ' title-icon'"></i> {{ title }} </h1>
        <p class="page-subtitle">{{ subtitle }}</p>
      </div>
      <div class="header-actions" v-if="actionText && actionRoute">
        <router-link :to="actionRoute" class="btn-primary"> <i :class="actionIcon" v-if="actionIcon"></i> {{ actionText }} </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: { type: String, required: true },
  subtitle: { type: String, default: '' },
  icon: { type: String, default: 'fa fa-home' },
  actionText: { type: String, default: '' },
  actionRoute: { type: Object, default: null },
  actionIcon: { type: String, default: '' },
  backgroundColor: { type: String, required: true }
});

const backgroundStyle = computed(() => {
  return { background: props.backgroundColor };
});
</script>

<style scoped>
@import '@/assets/scss/general/UserPageHeader.scss';
</style>