<!-- src/components/User/Pages/AuthPages/register.vue -->
<template>
    <div class="auth-page">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
                <div class="shape shape-6"></div>
            </div>
        </div>

        <div class="auth-container register">
            <!-- Left Side - Registration Form -->
            <div class="auth-form-section">
                <div class="form-container">
                    <div class="form-header">
                        <h2 class="form-title">Create Account</h2>
                        <p class="form-subtitle">Join thousands of professionals using AI responsibly</p>
                    </div>

                    <form class="auth-form" @submit.prevent="Register">
                        <!-- Personal Information -->
                        <div class="form-section">
                            <h3 class="section-title">Personal Information</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">First Name</label>
                                    <div class="input-wrapper">
                                        <i class="fa fa-user input-icon"></i>
                                        <input 
                                            type="text" 
                                            v-model="param.first_name" 
                                            class="form-input" 
                                            placeholder="John"
                                            required
                                        >
                                    </div>
                                    <div class="error-report text-danger"></div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Last Name</label>
                                    <div class="input-wrapper">
                                        <i class="fa fa-user input-icon"></i>
                                        <input 
                                            type="text" 
                                            v-model="param.last_name" 
                                            class="form-input" 
                                            placeholder="Doe"
                                            required
                                        >
                                    </div>
                                    <div class="error-report text-danger"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Email Address</label>
                                <div class="input-wrapper">
                                    <i class="fa fa-envelope input-icon"></i>
                                    <input 
                                        type="email" 
                                        v-model="param.email" 
                                        class="form-input" 
                                        placeholder="<EMAIL>"
                                        required
                                    >
                                </div>
                                <div class="error-report text-danger"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Phone Number <span class="optional">(Optional)</span></label>
                                <div class="input-wrapper">
                                    <i class="fa fa-phone input-icon"></i>
                                    <input 
                                        type="tel" 
                                        v-model="param.phone" 
                                        class="form-input" 
                                        placeholder="+****************"
                                    >
                                </div>
                                <div class="error-report text-danger"></div>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div class="form-section">
                            <h3 class="section-title">Professional Information</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Company Name <span class="optional">(Optional)</span></label>
                                    <div class="input-wrapper">
                                        <i class="fa fa-building input-icon"></i>
                                        <input 
                                            type="text" 
                                            v-model="param.company" 
                                            class="form-input" 
                                            placeholder="Your Company"
                                        >
                                    </div>
                                    <div class="error-report text-danger"></div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Website <span class="optional">(Optional)</span></label>
                                    <div class="input-wrapper">
                                        <i class="fa fa-globe input-icon"></i>
                                        <input 
                                            type="url" 
                                            v-model="param.website" 
                                            class="form-input" 
                                            placeholder="https://yourcompany.com"
                                        >
                                    </div>
                                    <div class="error-report text-danger"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Security -->
                        <div class="form-section">
                            <h3 class="section-title">Security</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Create a Strong Password</label>
                                    <div class="input-wrapper">
                                        <i class="fa fa-lock input-icon"></i>
                                        <input 
                                            :type="showPassword ? 'text' : 'password'" 
                                            v-model="param.password" 
                                            class="form-input" 
                                            placeholder="Password"
                                            autocomplete="new-password"
                                            required
                                        >
                                        <button 
                                            type="button" 
                                            class="password-toggle"
                                            @click="showPassword = !showPassword"
                                        >
                                            <i :class="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength">
                                        <div class="strength-meter">
                                            <div class="strength-bar" :class="passwordStrength.class" :style="{width: passwordStrength.width}"></div>
                                        </div>
                                        <span class="strength-text">{{ passwordStrength.text }}</span>
                                    </div>
                                    <div class="error-report text-danger"></div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Confirm Your Password</label>
                                    <div class="input-wrapper">
                                        <i class="fa fa-lock input-icon"></i>
                                        <input 
                                            :type="showConfirmPassword ? 'text' : 'password'" 
                                            v-model="param.password_confirmation" 
                                            class="form-input" 
                                            placeholder="Confirm"
                                            autocomplete="new-password"
                                            required
                                        >
                                        <button 
                                            type="button" 
                                            class="password-toggle"
                                            @click="showConfirmPassword = !showConfirmPassword"
                                        >
                                            <i :class="showConfirmPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                                        </button>
                                    </div>
                                    <div v-if="param.password_confirmation && !passwordsMatch" class="password-match-error">
                                        Passwords do not match
                                    </div>
                                    <div class="error-report text-danger"></div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="g-recaptcha-response" v-model="param['g-recaptcha-response']" id="g-recaptcha-response-v3">

                        <button 
                            type="submit" 
                            class="submit-btn"
                            :class="{ loading: loading }"
                            :disabled="loading || !formValid"
                        >
                            <span v-if="!loading" class="btn-content">
                                <i class="fa fa-user-plus"></i>
                                Create Account
                            </span>
                            <span v-else class="btn-content">
                                <i class="fa fa-spinner fa-spin"></i>
                                Creating Account...
                            </span>
                        </button>

                        <div class="form-divider">
                            <span>or</span>
                        </div>

                        <div class="auth-footer">
                            <p class="footer-text">
                                Already have an account? 
                                <router-link :to="{name: 'Login'}" class="auth-link">
                                    Sign In
                                </router-link>
                            </p>
                        </div>
                    </form>
                </div>

            </div>

            <!-- Right Side - Benefits & Features -->
            <div class="auth-branding">
                <div class="branding-content">
                    <div class="logo-section">
                        <a href="https://raidot.ai" class="brand-logo" target="_blank" rel="noopener noreferrer">
                            <span class="logo-text">Rai<span class="logo-accent">DOT</span></span>
                        </a>
                        <div class="logo-subtitle">AI Risk & Fairness Platform</div>
                    </div>
                    
                    <div class="welcome-section">
                        <h1 class="welcome-title">Join the AI Revolution</h1>
                        <p class="welcome-description">
                            Start your journey towards responsible AI development and deployment
                        </p>
                    </div>

                    <div class="benefits-list">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fa fa-check-circle"></i>
                            </div>
                            <div class="benefit-content">
                                <h3>Free Account</h3>
                                <p>Get started with basic features at no cost</p>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fa fa-rocket"></i>
                            </div>
                            <div class="benefit-content">
                                <h3>Instant Access</h3>
                                <p>Start evaluating AI risks immediately</p>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="benefit-content">
                                <h3>Expert Community</h3>
                                <p>Join thousands of AI professionals</p>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fa fa-certificate"></i>
                            </div>
                            <div class="benefit-content">
                                <h3>Certification Ready</h3>
                                <p>Earn recognized AI safety certificates</p>
                            </div>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="stats-section">
                        <div class="stat-item">
                            <div class="stat-number">10K+</div>
                            <div class="stat-label">Active Users</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50K+</div>
                            <div class="stat-label">Evaluations</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">99.9%</div>
                            <div class="stat-label">Uptime</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Header -->
        <div class="mobile-header">
            <a href="https://raidot.ai" class="mobile-logo" target="_blank" rel="noopener noreferrer">
                <span class="logo-text">Rai<span class="logo-accent">DOT</span></span>
            </a>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export default {
    name: 'RegisterPage',
    data() {
        return {
            loading: false,
            showPassword: false,
            showConfirmPassword: false,
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            param: {
                first_name: '',
                last_name: '',
                email: '',
                phone: '',
                company: '',
                website: '',
                password: '',
                password_confirmation: '',
                'g-recaptcha-response': ''
            }
        }
    },
    computed: {
        passwordStrength() {
            const password = this.param.password;
            if (!password) return { class: '', width: '0%', text: '' };
            
            let score = 0;
            let feedback = [];
            
            // Length check
            if (password.length >= 8) score += 1;
            else feedback.push('at least 8 characters');
            
            // Uppercase check
            if (/[A-Z]/.test(password)) score += 1;
            else feedback.push('uppercase letter');
            
            // Lowercase check
            if (/[a-z]/.test(password)) score += 1;
            else feedback.push('lowercase letter');
            
            // Number check
            if (/\d/.test(password)) score += 1;
            else feedback.push('number');
            
            // Special character check
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
            else feedback.push('special character');
            
            const strengths = [
                { class: 'weak', width: '20%', text: 'Very Weak' },
                { class: 'weak', width: '40%', text: 'Weak' },
                { class: 'medium', width: '60%', text: 'Fair' },
                { class: 'strong', width: '80%', text: 'Good' },
                { class: 'strong', width: '100%', text: 'Strong' }
            ];
            
            return strengths[score] || strengths[0];
        },
        passwordsMatch() {
            return !this.param.password_confirmation || 
                   this.param.password === this.param.password_confirmation;
        },
        formValid() {
            return this.param.first_name && 
                   this.param.last_name && 
                   this.param.email && 
                   this.param.password && 
                   this.param.password_confirmation && 
                   this.passwordsMatch &&
                   this.passwordStrength.class !== 'weak';
        }
    },
    methods: {
        async Register() {
            this.loading = true;
            ApiService.ClearErrorHandler();
            try {
                const res = await new Promise((resolve, reject) => {
                    ApiService.POST(ApiRoutes.UserRegister, this.param, (response) => {
                        if (parseInt(response.status) === 200) resolve(response);
                        else reject(response);
                    });
                });
                const { status, msg, token, user, pkg } = res;
                localStorage.setItem('JwtToken', token);
                localStorage.setItem('UserInfo', JSON.stringify(user));
                localStorage.setItem('Subscription', JSON.stringify(pkg));
                this.$router.push({ name: 'Dashboard' });
            } catch (error) {
                ApiService.ErrorHandler(error.error || error);
            }
            this.loading = false;
        }
    },
    created() {
        if (this.UserInfo != null) {
            this.$router.push({ name: 'Dashboard' });
        }
    },
    mounted() {
        window.scrollTo(0, 0);
        
        // Load reCAPTCHA
        if (window.grecaptcha) {
            grecaptcha.ready(() => {
                grecaptcha.execute(import.meta.env.VITE_RECAPTCHA_SITE_KEY, { action: 'submit' }).then(token => {
                    this.param['g-recaptcha-response'] = token;
                    const target = document.getElementById('g-recaptcha-response-v3');
                    if (target) target.value = token;
                });
            });
        }
    }
}
</script>

<style scoped>
/* Base auth page styles */
.auth-page {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    overflow: hidden;
}

.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 5%;
    animation-delay: -1s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 70%;
    right: 10%;
    animation-delay: -3s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 15%;
    animation-delay: -2s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 5%;
    right: 25%;
    animation-delay: -4s;
}

.shape-5 {
    width: 60px;
    height: 60px;
    bottom: 5%;
    right: 5%;
    animation-delay: -0.5s;
}

.shape-6 {
    width: 90px;
    height: 90px;
    top: 40%;
    left: 3%;
    animation-delay: -2.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Main Container - Register Version */
.auth-container.register {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1400px;
    width: 100%;
    margin: 2rem;
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    position: relative;
    z-index: 1;
    min-height: 700px;
}

/* Form Section - Left Side for Registration */
.auth-form-section {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    /*justify-content: center;*/
    position: relative;
    overflow-y: auto;
    max-height: 100vh;
}

.form-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.form-subtitle {
    color: #6b7280;
    font-size: 1rem;
    margin: 0;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Form Sections */
.form-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.optional {
    color: #9ca3af;
    font-weight: 400;
    font-size: 0.75rem;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.1);
}

.form-input.is-invalid {
    border-color: #ef4444;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #059669;
}

/* Password Strength Indicator */
.password-strength {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.strength-meter {
    flex: 1;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-bar.weak {
    background: #ef4444;
}

.strength-bar.medium {
    background: #f59e0b;
}

.strength-bar.strong {
    background: #059669;
}

.strength-text {
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 60px;
    text-align: right;
}

.password-match-error {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.submit-btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.form-divider {
    position: relative;
    text-align: center;
    margin: 0.5rem 0;
}

.form-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
    z-index: 1;
}

.form-divider span {
    background: white;
    color: #9ca3af;
    padding: 0 1rem;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

.auth-footer {
    text-align: center;
}

.footer-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.auth-link {
    color: #059669;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.auth-link:hover {
    color: #047857;
    text-decoration: underline;
}

/* Right Side - Branding */
.auth-branding {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    padding: 3rem;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.auth-branding::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.branding-content {
    width: 100%;
    position: relative;
    z-index: 1;
}

.logo-section {
    margin-bottom: 3rem;
}

.brand-logo {
    display: inline-block;
    text-decoration: none;
}

.logo-text {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
}

.logo-accent {
    color: #ffd700;
}

.logo-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.welcome-section {
    margin-bottom: 3rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.welcome-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.benefit-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    backdrop-filter: blur(10px);
}

.benefit-content h3 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.benefit-content p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
}

/* Stats Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.security-badge {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.security-badge i {
    color: #059669;
}

/* Mobile Header */
.mobile-header {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.mobile-logo {
    text-decoration: none;
}

.mobile-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1f2937;
}

.mobile-logo .logo-accent {
    color: #059669;
}

/* Error States */
.error-report {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .auth-container.register {
        margin: 1rem;
        max-width: 1100px;
    }
    
    .auth-branding,
    .auth-form-section {
        padding: 2rem;
    }
    
    .benefits-list {
        gap: 1rem;
    }
    
    .stats-section {
        gap: 1rem;
    }
}

@media (max-width: 1024px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .stats-section {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .auth-page {
        padding-top: 80px;
    }
    
    .mobile-header {
        display: block;
    }
    
    .auth-container.register {
        grid-template-columns: 1fr;
        margin: 1rem 0.5rem;
        min-height: auto;
    }
    
    .auth-form-section {
        order: 1;
        padding: 2rem 1.5rem;
        max-height: none;
    }
    
    .auth-branding {
        order: 2;
        padding: 2rem 1.5rem 1.5rem;
        text-align: center;
    }
    
    .welcome-section {
        margin-bottom: 2rem;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .benefits-list {
        flex-direction: row;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }
    
    .benefit-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
        max-width: 120px;
    }
    
    .benefit-content h3 {
        font-size: 0.875rem;
    }
    
    .benefit-content p {
        font-size: 0.75rem;
    }
    
    .stats-section {
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .security-badge {
        position: relative;
        bottom: auto;
        left: auto;
        transform: none;
        margin-top: 1rem;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .auth-container.register {
        margin: 0.5rem;
        border-radius: 16px;
    }
    
    .auth-branding,
    .auth-form-section {
        padding: 1.5rem 1rem;
    }
    
    .logo-text {
        font-size: 2rem;
    }
    
    .welcome-title {
        font-size: 1.25rem;
    }
    
    .welcome-description {
        font-size: 1rem;
    }
    
    .form-title {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 1rem;
    }
    
    .benefits-list {
        gap: 0.75rem;
    }
    
    .benefit-item {
        max-width: 100px;
    }
    
    .benefit-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .stats-section {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.625rem;
    }
}

/* Focus states for accessibility */
.form-input:focus,
.submit-btn:focus,
.password-toggle:focus,
.auth-link:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .form-input {
        border-width: 3px;
    }
    
    .submit-btn {
        border: 2px solid #000;
    }
    
    .strength-bar.weak {
        background: #dc2626;
    }
    
    .strength-bar.medium {
        background: #ea580c;
    }
    
    .strength-bar.strong {
        background: #047857;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .shape,
    .submit-btn,
    .form-input,
    .password-toggle,
    .auth-link,
    .strength-bar {
        animation: none;
        transition: none;
    }
    
    @keyframes float {
        from, to {
            transform: translateY(0px) rotate(0deg);
        }
    }
}

/* Loading state animations */
.submit-btn.loading {
    pointer-events: none;
}

.fa-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Form validation styles */
.form-input:valid {
    border-color: #10b981;
}

.form-input:invalid:not(:placeholder-shown) {
    border-color: #ef4444;
}

/* Smooth scrolling for form sections */
@media (max-height: 800px) {
    .auth-form-section {
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #d1d5db transparent;
    }
    
    .auth-form-section::-webkit-scrollbar {
        width: 6px;
    }
    
    .auth-form-section::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .auth-form-section::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 3px;
    }
    
    .auth-form-section::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }
}
</style>